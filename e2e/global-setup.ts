import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('Starting global setup...');

  // 启动浏览器进行预热
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 等待前端服务启动
    console.log('Waiting for frontend service...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    console.log('Frontend service is ready');

    // 等待后端服务启动
    console.log('Waiting for backend service...');
    const response = await page.request.get('http://localhost:8000/api/health');
    if (!response.ok()) {
      throw new Error('Backend service is not ready');
    }
    console.log('Backend service is ready');

    // 创建测试用户（如果需要）
    await setupTestData();

  } catch (error) {
    console.error('Global setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }

  console.log('Global setup completed');
}

async function setupTestData() {
  // 这里可以设置测试数据
  // 例如：创建测试用户、清理数据库等
  console.log('Setting up test data...');
  
  // 可以通过API创建测试用户
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 创建测试用户
    const response = await page.request.post('http://localhost:8000/api/auth/register', {
      data: {
        username: 'e2etest',
        email: '<EMAIL>',
        password: 'Test123456',
        confirmPassword: 'Test123456',
      },
    });

    if (response.ok()) {
      console.log('Test user created successfully');
    } else {
      console.log('Test user might already exist or creation failed');
    }
  } catch (error) {
    console.log('Test user setup failed:', error);
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup;
