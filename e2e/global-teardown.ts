import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('Starting global teardown...');

  // 清理测试数据
  await cleanupTestData();

  console.log('Global teardown completed');
}

async function cleanupTestData() {
  console.log('Cleaning up test data...');
  
  // 这里可以清理测试数据
  // 例如：删除测试用户、清理数据库等
  
  try {
    // 可以通过API清理数据
    // 或者重置数据库到初始状态
    console.log('Test data cleanup completed');
  } catch (error) {
    console.error('Test data cleanup failed:', error);
  }
}

export default globalTeardown;
