{"name": "coze-clone-e2e", "version": "1.0.0", "description": "End-to-end tests for Coze Clone Platform", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "test:codegen": "playwright codegen", "install:browsers": "playwright install", "install:deps": "playwright install-deps"}, "keywords": ["playwright", "e2e", "testing", "automation"], "author": "Coze Clone Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "dependencies": {}}