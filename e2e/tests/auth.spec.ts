import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // 确保每个测试开始时都是未登录状态
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
  });

  test('should register a new user successfully', async ({ page }) => {
    await page.goto('/register');

    // 验证注册页面元素
    await expect(page.getByText('Coze Clone Platform')).toBeVisible();
    await expect(page.getByText('创建您的账户')).toBeVisible();

    // 填写注册表单
    const timestamp = Date.now();
    const testEmail = `test${timestamp}@example.com`;
    const testUsername = `testuser${timestamp}`;

    await page.getByLabel('用户名').fill(testUsername);
    await page.getByLabel('邮箱').fill(testEmail);
    await page.getByLabel('密码', { exact: true }).fill('Test123456');
    await page.getByLabel('确认密码').fill('Test123456');

    // 提交表单
    await page.getByRole('button', { name: '注册' }).click();

    // 验证注册成功并重定向到首页
    await expect(page).toHaveURL('/');
    await expect(page.getByText('欢迎使用 Coze Clone Platform')).toBeVisible();

    // 验证用户已登录
    await expect(page.getByText(testUsername)).toBeVisible();
  });

  test('should show validation errors for invalid registration data', async ({ page }) => {
    await page.goto('/register');

    // 提交空表单
    await page.getByRole('button', { name: '注册' }).click();

    // 验证错误消息
    await expect(page.getByText('请输入用户名')).toBeVisible();
    await expect(page.getByText('请输入邮箱')).toBeVisible();
    await expect(page.getByText('请输入密码')).toBeVisible();

    // 测试无效邮箱
    await page.getByLabel('邮箱').fill('invalid-email');
    await page.getByRole('button', { name: '注册' }).click();
    await expect(page.getByText('请输入有效的邮箱地址')).toBeVisible();

    // 测试弱密码
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码', { exact: true }).fill('123');
    await page.getByRole('button', { name: '注册' }).click();
    await expect(page.getByText('密码至少6个字符')).toBeVisible();

    // 测试密码不匹配
    await page.getByLabel('密码', { exact: true }).fill('Test123456');
    await page.getByLabel('确认密码').fill('Different123');
    await page.getByRole('button', { name: '注册' }).click();
    await expect(page.getByText('两次输入的密码不一致')).toBeVisible();
  });

  test('should login with valid credentials', async ({ page }) => {
    await page.goto('/login');

    // 验证登录页面元素
    await expect(page.getByText('登录您的账户')).toBeVisible();

    // 使用预设的测试用户登录
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('Test123456');

    // 提交登录表单
    await page.getByRole('button', { name: '登录' }).click();

    // 验证登录成功
    await expect(page).toHaveURL('/');
    await expect(page.getByText('欢迎使用 Coze Clone Platform')).toBeVisible();
    await expect(page.getByText('e2etest')).toBeVisible();
  });

  test('should show error for invalid login credentials', async ({ page }) => {
    await page.goto('/login');

    // 使用错误的凭据
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('wrongpassword');

    await page.getByRole('button', { name: '登录' }).click();

    // 验证错误消息
    await expect(page.getByText('Invalid email or password')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // 先登录
    await page.goto('/login');
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('Test123456');
    await page.getByRole('button', { name: '登录' }).click();

    await expect(page).toHaveURL('/');

    // 点击用户菜单
    await page.getByText('e2etest').click();

    // 点击退出登录
    await page.getByText('退出登录').click();

    // 验证重定向到登录页
    await expect(page).toHaveURL('/login');
    await expect(page.getByText('登录您的账户')).toBeVisible();
  });

  test('should redirect to login when accessing protected routes', async ({ page }) => {
    // 尝试访问受保护的路由
    await page.goto('/generation');

    // 应该重定向到登录页
    await expect(page).toHaveURL('/login');
    await expect(page.getByText('登录您的账户')).toBeVisible();
  });

  test('should redirect to intended page after login', async ({ page }) => {
    // 尝试访问受保护的路由
    await page.goto('/generation');
    await expect(page).toHaveURL('/login');

    // 登录
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('Test123456');
    await page.getByRole('button', { name: '登录' }).click();

    // 应该重定向到原本想访问的页面
    await expect(page).toHaveURL('/generation');
  });

  test('should handle password visibility toggle', async ({ page }) => {
    await page.goto('/login');

    const passwordInput = page.getByLabel('密码');
    
    // 默认应该是password类型
    await expect(passwordInput).toHaveAttribute('type', 'password');

    // 点击显示密码按钮
    await page.locator('.ant-input-password-icon').click();
    await expect(passwordInput).toHaveAttribute('type', 'text');

    // 再次点击隐藏密码
    await page.locator('.ant-input-password-icon').click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
  });

  test('should show loading state during authentication', async ({ page }) => {
    await page.goto('/login');

    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('Test123456');

    // 点击登录按钮
    const loginButton = page.getByRole('button', { name: '登录' });
    await loginButton.click();

    // 验证加载状态
    await expect(page.getByText('登录中...')).toBeVisible();
    await expect(loginButton).toBeDisabled();

    // 等待登录完成
    await expect(page).toHaveURL('/');
  });

  test('should persist login state after page refresh', async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('Test123456');
    await page.getByRole('button', { name: '登录' }).click();

    await expect(page).toHaveURL('/');

    // 刷新页面
    await page.reload();

    // 验证仍然保持登录状态
    await expect(page.getByText('欢迎使用 Coze Clone Platform')).toBeVisible();
    await expect(page.getByText('e2etest')).toBeVisible();
  });

  test('should handle session expiration', async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('Test123456');
    await page.getByRole('button', { name: '登录' }).click();

    await expect(page).toHaveURL('/');

    // 模拟token过期（清除localStorage中的token）
    await page.evaluate(() => {
      localStorage.removeItem('token');
    });

    // 尝试访问需要认证的API
    await page.goto('/generation');

    // 应该重定向到登录页
    await expect(page).toHaveURL('/login');
  });
});
