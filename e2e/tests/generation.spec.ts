import { test, expect } from '@playwright/test';

test.describe('Content Generation Flow', () => {
  test.beforeEach(async ({ page }) => {
    // 登录测试用户
    await page.goto('/login');
    await page.getByLabel('邮箱').fill('<EMAIL>');
    await page.getByLabel('密码').fill('Test123456');
    await page.getByRole('button', { name: '登录' }).click();
    await expect(page).toHaveURL('/');
  });

  test('should create a new generation successfully', async ({ page }) => {
    await page.goto('/generation');

    // 验证生成页面元素
    await expect(page.getByText('内容生成')).toBeVisible();
    await expect(page.getByText('生成结果')).toBeVisible();

    // 填写生成表单
    const promptText = 'Write a short article about artificial intelligence in 2024';
    await page.getByLabel('输入您的需求').fill(promptText);

    // 选择内容类型
    await page.getByRole('combobox').first().click();
    await page.getByText('文章').click();

    // 调整参数
    const temperatureSlider = page.locator('.ant-slider-handle').first();
    await temperatureSlider.hover();
    await page.mouse.down();
    await page.mouse.move(50, 0);
    await page.mouse.up();

    // 提交生成请求
    await page.getByRole('button', { name: '开始生成' }).click();

    // 验证生成状态
    await expect(page.getByText('生成中...')).toBeVisible();
    await expect(page.getByRole('progressbar')).toBeVisible();

    // 等待生成完成（模拟）
    await page.waitForTimeout(3000);

    // 验证生成结果（这里需要根据实际的mock数据调整）
    await expect(page.getByText('生成完成')).toBeVisible({ timeout: 30000 });
  });

  test('should show validation errors for empty prompt', async ({ page }) => {
    await page.goto('/generation');

    // 不填写任何内容直接提交
    await page.getByRole('button', { name: '开始生成' }).click();

    // 验证错误消息
    await expect(page.getByText('请输入生成需求')).toBeVisible();
  });

  test('should handle form options correctly', async ({ page }) => {
    await page.goto('/generation');

    // 测试内容类型选择
    await page.getByRole('combobox').first().click();
    await expect(page.getByText('文章')).toBeVisible();
    await expect(page.getByText('故事')).toBeVisible();
    await expect(page.getByText('代码')).toBeVisible();
    await page.getByText('代码').click();

    // 测试滑块控件
    const temperatureSlider = page.locator('.ant-slider').first();
    await expect(temperatureSlider).toBeVisible();

    const maxTokensSlider = page.locator('.ant-slider').last();
    await expect(maxTokensSlider).toBeVisible();

    // 验证滑块标签
    await expect(page.getByText('创意程度')).toBeVisible();
    await expect(page.getByText('最大长度')).toBeVisible();
  });

  test('should copy generated content to clipboard', async ({ page }) => {
    // 假设已有生成结果的页面
    await page.goto('/generation/test-generation-id');

    // 等待内容加载
    await expect(page.getByText('生成结果')).toBeVisible();

    // 点击复制按钮
    await page.getByRole('button', { name: '复制' }).click();

    // 验证复制成功消息
    await expect(page.getByText('内容已复制到剪贴板')).toBeVisible();
  });

  test('should download generated content', async ({ page }) => {
    await page.goto('/generation/test-generation-id');

    // 等待内容加载
    await expect(page.getByText('生成结果')).toBeVisible();

    // 设置下载监听
    const downloadPromise = page.waitForEvent('download');

    // 点击下载按钮
    await page.getByRole('button', { name: '下载' }).click();

    // 验证下载开始
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/generation.*\.(md|pdf)$/);
  });

  test('should regenerate content', async ({ page }) => {
    await page.goto('/generation/test-generation-id');

    // 等待内容加载
    await expect(page.getByText('生成结果')).toBeVisible();

    // 点击重新生成按钮
    await page.getByRole('button', { name: '重新生成' }).click();

    // 验证重新生成状态
    await expect(page.getByText('生成中...')).toBeVisible();
    await expect(page.getByRole('progressbar')).toBeVisible();
  });

  test('should handle generation progress updates', async ({ page }) => {
    await page.goto('/generation');

    // 填写表单
    await page.getByLabel('输入您的需求').fill('Test generation prompt');
    await page.getByRole('button', { name: '开始生成' }).click();

    // 验证进度条出现
    await expect(page.getByRole('progressbar')).toBeVisible();

    // 验证进度文本
    await expect(page.getByText('正在生成内容，请稍候...')).toBeVisible();

    // 验证按钮状态
    await expect(page.getByRole('button', { name: '生成中...' })).toBeDisabled();
  });

  test('should handle generation failure', async ({ page }) => {
    // 这个测试需要模拟生成失败的情况
    await page.goto('/generation');

    // 使用特殊的prompt来触发失败（需要后端配合）
    await page.getByLabel('输入您的需求').fill('TRIGGER_FAILURE');
    await page.getByRole('button', { name: '开始生成' }).click();

    // 验证失败状态
    await expect(page.getByText('生成失败')).toBeVisible({ timeout: 30000 });
    await expect(page.getByText('生成失败')).toBeVisible();
  });

  test('should display generation metadata', async ({ page }) => {
    await page.goto('/generation/test-generation-id');

    // 等待内容加载
    await expect(page.getByText('生成结果')).toBeVisible();

    // 验证元数据显示
    await expect(page.getByText('生成信息')).toBeVisible();
    await expect(page.getByText(/生成时间:/)).toBeVisible();
    await expect(page.getByText(/耗时:/)).toBeVisible();
    await expect(page.getByText(/Token数:/)).toBeVisible();
  });

  test('should render markdown content correctly', async ({ page }) => {
    await page.goto('/generation/test-generation-id');

    // 等待内容加载
    await expect(page.getByText('生成结果')).toBeVisible();

    // 验证markdown渲染
    await expect(page.locator('h1, h2, h3')).toBeVisible();
    await expect(page.locator('p')).toBeVisible();

    // 验证代码块渲染（如果有）
    const codeBlocks = page.locator('pre code');
    if (await codeBlocks.count() > 0) {
      await expect(codeBlocks.first()).toBeVisible();
    }
  });

  test('should handle image generation results', async ({ page }) => {
    // 假设生成结果包含图片
    await page.goto('/generation/test-generation-with-images');

    // 等待内容加载
    await expect(page.getByText('生成结果')).toBeVisible();

    // 验证图片显示
    await expect(page.getByText('生成的图片')).toBeVisible();
    
    const images = page.locator('img[alt*="Generated"]');
    if (await images.count() > 0) {
      await expect(images.first()).toBeVisible();
    }
  });

  test('should work on mobile devices', async ({ page }) => {
    // 设置移动设备视口
    await page.setViewportSize({ width: 375, height: 667 });

    await page.goto('/generation');

    // 验证移动端布局
    await expect(page.getByText('内容生成')).toBeVisible();
    await expect(page.getByText('生成结果')).toBeVisible();

    // 验证表单在移动端可用
    await page.getByLabel('输入您的需求').fill('Mobile test prompt');
    await page.getByRole('button', { name: '开始生成' }).click();

    await expect(page.getByText('生成中...')).toBeVisible();
  });

  test('should handle keyboard shortcuts', async ({ page }) => {
    await page.goto('/generation');

    // 填写表单
    await page.getByLabel('输入您的需求').fill('Test prompt');

    // 使用Ctrl+Enter提交（如果支持）
    await page.keyboard.press('Control+Enter');

    // 验证是否触发生成
    await expect(page.getByText('生成中...')).toBeVisible();
  });

  test('should preserve form data on page refresh', async ({ page }) => {
    await page.goto('/generation');

    // 填写表单
    const promptText = 'Test prompt for refresh';
    await page.getByLabel('输入您的需求').fill(promptText);

    // 选择类型
    await page.getByRole('combobox').first().click();
    await page.getByText('文章').click();

    // 刷新页面
    await page.reload();

    // 验证表单数据是否保留（如果实现了自动保存）
    const promptValue = await page.getByLabel('输入您的需求').inputValue();
    if (promptValue) {
      expect(promptValue).toBe(promptText);
    }
  });

  test('should handle concurrent generations', async ({ page, context }) => {
    // 打开多个标签页
    const page2 = await context.newPage();

    // 在第一个标签页开始生成
    await page.goto('/generation');
    await page.getByLabel('输入您的需求').fill('First generation');
    await page.getByRole('button', { name: '开始生成' }).click();

    // 在第二个标签页开始生成
    await page2.goto('/generation');
    await page2.getByLabel('输入您的需求').fill('Second generation');
    await page2.getByRole('button', { name: '开始生成' }).click();

    // 验证两个生成都在进行
    await expect(page.getByText('生成中...')).toBeVisible();
    await expect(page2.getByText('生成中...')).toBeVisible();

    await page2.close();
  });
});
