{"name": "coze-clone-platform", "version": "1.0.0", "description": "A Coze platform alternative using MCP Chrome for browser automation", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "keywords": ["coze", "ai", "mcp", "chrome", "automation", "browser", "proxy"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend"]}