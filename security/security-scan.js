const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 安全测试配置
const config = {
  baseUrl: process.env.BASE_URL || 'http://localhost:8000',
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  timeout: 10000,
  maxRetries: 3,
};

// 测试结果存储
const results = {
  timestamp: new Date().toISOString(),
  vulnerabilities: [],
  passed: [],
  failed: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
  },
};

// 日志函数
function log(level, message, details = null) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  if (details) {
    console.log(JSON.stringify(details, null, 2));
  }
}

// 添加漏洞
function addVulnerability(name, severity, description, evidence = null) {
  const vulnerability = {
    name,
    severity,
    description,
    evidence,
    timestamp: new Date().toISOString(),
  };
  
  results.vulnerabilities.push(vulnerability);
  results.failed.push(name);
  results.summary.failed++;
  results.summary[severity]++;
  
  log('vulnerability', `${severity.toUpperCase()}: ${name}`, { description, evidence });
}

// 添加通过的测试
function addPassed(name, description) {
  results.passed.push({ name, description });
  results.summary.passed++;
  log('pass', `PASSED: ${name}`);
}

// HTTP请求函数
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const response = await axios({
      method,
      url,
      data,
      headers: {
        'User-Agent': 'Security-Scanner/1.0',
        ...headers,
      },
      timeout: config.timeout,
      validateStatus: () => true, // 不抛出错误，让我们处理所有状态码
    });
    
    return response;
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      throw new Error('Service is not running');
    }
    throw error;
  }
}

// 1. SQL注入测试
async function testSqlInjection() {
  log('info', 'Testing SQL Injection vulnerabilities...');
  
  const payloads = [
    "' OR '1'='1",
    "'; DROP TABLE users; --",
    "' UNION SELECT * FROM users --",
    "admin'--",
    "' OR 1=1#",
  ];
  
  const endpoints = [
    '/api/auth/login',
    '/api/generation',
    '/api/user/profile',
  ];
  
  for (const endpoint of endpoints) {
    for (const payload of payloads) {
      try {
        const response = await makeRequest('POST', `${config.baseUrl}${endpoint}`, {
          email: payload,
          password: payload,
          prompt: payload,
        });
        
        // 检查响应中是否包含SQL错误信息
        const body = response.data || '';
        const sqlErrors = [
          'sql syntax',
          'mysql_fetch',
          'postgresql',
          'ora-',
          'sqlite',
          'syntax error',
        ];
        
        const hasSqlError = sqlErrors.some(error => 
          body.toString().toLowerCase().includes(error)
        );
        
        if (hasSqlError) {
          addVulnerability(
            'SQL Injection',
            'high',
            `SQL injection vulnerability detected in ${endpoint}`,
            { payload, response: body.substring(0, 500) }
          );
        }
      } catch (error) {
        // 忽略网络错误
      }
    }
  }
  
  addPassed('SQL Injection', 'No SQL injection vulnerabilities detected');
}

// 2. XSS测试
async function testXss() {
  log('info', 'Testing XSS vulnerabilities...');
  
  const payloads = [
    '<script>alert("XSS")</script>',
    '"><script>alert("XSS")</script>',
    "javascript:alert('XSS')",
    '<img src=x onerror=alert("XSS")>',
    '<svg onload=alert("XSS")>',
  ];
  
  for (const payload of payloads) {
    try {
      const response = await makeRequest('POST', `${config.baseUrl}/api/generation`, {
        prompt: payload,
      });
      
      // 检查响应是否直接返回了未转义的脚本
      const body = response.data || '';
      if (body.toString().includes(payload) && !body.toString().includes('&lt;script&gt;')) {
        addVulnerability(
          'Cross-Site Scripting (XSS)',
          'high',
          'XSS vulnerability detected - user input not properly escaped',
          { payload, response: body.substring(0, 500) }
        );
      }
    } catch (error) {
      // 忽略网络错误
    }
  }
  
  addPassed('XSS', 'No XSS vulnerabilities detected');
}

// 3. 认证绕过测试
async function testAuthBypass() {
  log('info', 'Testing authentication bypass...');
  
  const protectedEndpoints = [
    '/api/generation',
    '/api/user/profile',
    '/api/generation/stats',
  ];
  
  for (const endpoint of protectedEndpoints) {
    try {
      // 测试无token访问
      const response = await makeRequest('GET', `${config.baseUrl}${endpoint}`);
      
      if (response.status === 200) {
        addVulnerability(
          'Authentication Bypass',
          'critical',
          `Protected endpoint ${endpoint} accessible without authentication`,
          { endpoint, status: response.status }
        );
      }
      
      // 测试无效token
      const invalidTokenResponse = await makeRequest('GET', `${config.baseUrl}${endpoint}`, null, {
        'Authorization': 'Bearer invalid-token',
      });
      
      if (invalidTokenResponse.status === 200) {
        addVulnerability(
          'Authentication Bypass',
          'critical',
          `Protected endpoint ${endpoint} accessible with invalid token`,
          { endpoint, status: invalidTokenResponse.status }
        );
      }
    } catch (error) {
      // 忽略网络错误
    }
  }
  
  addPassed('Authentication Bypass', 'Authentication properly enforced');
}

// 4. 敏感信息泄露测试
async function testInformationDisclosure() {
  log('info', 'Testing information disclosure...');
  
  const sensitiveEndpoints = [
    '/api/health',
    '/api/debug',
    '/api/config',
    '/.env',
    '/package.json',
    '/api/users',
  ];
  
  for (const endpoint of sensitiveEndpoints) {
    try {
      const response = await makeRequest('GET', `${config.baseUrl}${endpoint}`);
      
      if (response.status === 200) {
        const body = response.data || '';
        const sensitiveInfo = [
          'password',
          'secret',
          'key',
          'token',
          'database',
          'connection',
        ];
        
        const hasSensitiveInfo = sensitiveInfo.some(info => 
          body.toString().toLowerCase().includes(info)
        );
        
        if (hasSensitiveInfo) {
          addVulnerability(
            'Information Disclosure',
            'medium',
            `Sensitive information exposed in ${endpoint}`,
            { endpoint, response: body.substring(0, 500) }
          );
        }
      }
    } catch (error) {
      // 忽略网络错误
    }
  }
  
  addPassed('Information Disclosure', 'No sensitive information disclosure detected');
}

// 5. CSRF测试
async function testCsrf() {
  log('info', 'Testing CSRF vulnerabilities...');
  
  try {
    // 测试状态改变操作是否需要CSRF token
    const response = await makeRequest('POST', `${config.baseUrl}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password',
    }, {
      'Origin': 'http://malicious-site.com',
      'Referer': 'http://malicious-site.com',
    });
    
    // 检查是否有CSRF保护
    const headers = response.headers || {};
    const hasCsrfProtection = headers['x-csrf-token'] || headers['set-cookie']?.includes('csrf');
    
    if (!hasCsrfProtection && response.status !== 403) {
      addVulnerability(
        'Cross-Site Request Forgery (CSRF)',
        'medium',
        'CSRF protection not implemented for state-changing operations',
        { endpoint: '/api/auth/login', headers: Object.keys(headers) }
      );
    }
  } catch (error) {
    // 忽略网络错误
  }
  
  addPassed('CSRF', 'CSRF protection properly implemented');
}

// 6. 安全头检查
async function testSecurityHeaders() {
  log('info', 'Testing security headers...');
  
  try {
    const response = await makeRequest('GET', config.frontendUrl);
    const headers = response.headers || {};
    
    const requiredHeaders = {
      'x-content-type-options': 'nosniff',
      'x-frame-options': ['DENY', 'SAMEORIGIN'],
      'x-xss-protection': '1; mode=block',
      'strict-transport-security': null, // 应该存在
      'content-security-policy': null, // 应该存在
    };
    
    for (const [header, expectedValue] of Object.entries(requiredHeaders)) {
      const actualValue = headers[header];
      
      if (!actualValue) {
        addVulnerability(
          'Missing Security Header',
          'medium',
          `Missing security header: ${header}`,
          { header, expected: expectedValue }
        );
      } else if (Array.isArray(expectedValue) && !expectedValue.includes(actualValue)) {
        addVulnerability(
          'Incorrect Security Header',
          'low',
          `Incorrect value for security header: ${header}`,
          { header, actual: actualValue, expected: expectedValue }
        );
      } else if (typeof expectedValue === 'string' && actualValue !== expectedValue) {
        addVulnerability(
          'Incorrect Security Header',
          'low',
          `Incorrect value for security header: ${header}`,
          { header, actual: actualValue, expected: expectedValue }
        );
      }
    }
  } catch (error) {
    log('error', 'Failed to test security headers', error.message);
  }
  
  addPassed('Security Headers', 'Security headers properly configured');
}

// 7. 暴力破解测试
async function testBruteForce() {
  log('info', 'Testing brute force protection...');
  
  const attempts = [];
  
  // 尝试多次登录失败
  for (let i = 0; i < 10; i++) {
    try {
      const start = Date.now();
      const response = await makeRequest('POST', `${config.baseUrl}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'wrong-password',
      });
      const end = Date.now();
      
      attempts.push({
        attempt: i + 1,
        status: response.status,
        duration: end - start,
      });
      
      // 检查是否被限制
      if (response.status === 429) {
        addPassed('Brute Force Protection', 'Rate limiting properly implemented');
        return;
      }
    } catch (error) {
      // 忽略网络错误
    }
  }
  
  // 如果没有被限制，可能存在暴力破解漏洞
  addVulnerability(
    'Brute Force Vulnerability',
    'medium',
    'No rate limiting detected for login attempts',
    { attempts: attempts.length }
  );
}

// 生成报告
function generateReport() {
  results.summary.total = results.summary.passed + results.summary.failed;
  
  const reportDir = path.join(__dirname, 'reports');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(reportDir, `security-report-${timestamp}.json`);
  
  fs.writeFileSync(reportFile, JSON.stringify(results, null, 2));
  
  // 生成HTML报告
  const htmlReport = generateHtmlReport();
  const htmlFile = path.join(reportDir, `security-report-${timestamp}.html`);
  fs.writeFileSync(htmlFile, htmlReport);
  
  log('info', `Security report generated: ${reportFile}`);
  return results;
}

// 生成HTML报告
function generateHtmlReport() {
  const severityColors = {
    critical: '#dc3545',
    high: '#fd7e14',
    medium: '#ffc107',
    low: '#28a745',
  };
  
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Security Scan Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0; }
        .summary-item { text-align: center; padding: 15px; border-radius: 5px; }
        .vulnerability { margin: 15px 0; padding: 15px; border-left: 4px solid; border-radius: 3px; }
        .critical { border-color: ${severityColors.critical}; background: #f8d7da; }
        .high { border-color: ${severityColors.high}; background: #fff3cd; }
        .medium { border-color: ${severityColors.medium}; background: #fff3cd; }
        .low { border-color: ${severityColors.low}; background: #d1edff; }
        .passed { background: #d4edda; border-left: 4px solid #28a745; }
        .evidence { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Security Scan Report</h1>
        <p>Generated: ${results.timestamp}</p>
        <p>Target: ${config.baseUrl}</p>
    </div>
    
    <div class="summary">
        <div class="summary-item" style="background: #e9ecef;">
            <h3>${results.summary.total}</h3>
            <p>Total Tests</p>
        </div>
        <div class="summary-item" style="background: #d4edda;">
            <h3>${results.summary.passed}</h3>
            <p>Passed</p>
        </div>
        <div class="summary-item" style="background: #f8d7da;">
            <h3>${results.summary.failed}</h3>
            <p>Failed</p>
        </div>
        <div class="summary-item" style="background: ${severityColors.critical}; color: white;">
            <h3>${results.summary.critical}</h3>
            <p>Critical</p>
        </div>
        <div class="summary-item" style="background: ${severityColors.high}; color: white;">
            <h3>${results.summary.high}</h3>
            <p>High</p>
        </div>
        <div class="summary-item" style="background: ${severityColors.medium};">
            <h3>${results.summary.medium}</h3>
            <p>Medium</p>
        </div>
        <div class="summary-item" style="background: ${severityColors.low}; color: white;">
            <h3>${results.summary.low}</h3>
            <p>Low</p>
        </div>
    </div>
    
    <h2>Vulnerabilities Found</h2>
    ${results.vulnerabilities.length === 0 ? '<p>No vulnerabilities found! 🎉</p>' : ''}
    ${results.vulnerabilities.map(vuln => `
        <div class="vulnerability ${vuln.severity}">
            <h3>${vuln.name} (${vuln.severity.toUpperCase()})</h3>
            <p>${vuln.description}</p>
            ${vuln.evidence ? `<div class="evidence">${JSON.stringify(vuln.evidence, null, 2)}</div>` : ''}
        </div>
    `).join('')}
    
    <h2>Passed Tests</h2>
    ${results.passed.map(test => `
        <div class="passed">
            <h4>✅ ${test.name}</h4>
            <p>${test.description}</p>
        </div>
    `).join('')}
</body>
</html>
  `;
}

// 主函数
async function main() {
  log('info', 'Starting security scan...');
  
  try {
    await testSqlInjection();
    await testXss();
    await testAuthBypass();
    await testInformationDisclosure();
    await testCsrf();
    await testSecurityHeaders();
    await testBruteForce();
    
    const report = generateReport();
    
    log('info', 'Security scan completed');
    log('info', `Summary: ${report.summary.total} tests, ${report.summary.passed} passed, ${report.summary.failed} failed`);
    
    if (report.summary.critical > 0 || report.summary.high > 0) {
      log('error', 'Critical or high severity vulnerabilities found!');
      process.exit(1);
    }
    
  } catch (error) {
    log('error', 'Security scan failed', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = {
  main,
  testSqlInjection,
  testXss,
  testAuthBypass,
  testInformationDisclosure,
  testCsrf,
  testSecurityHeaders,
  testBruteForce,
  generateReport,
};
