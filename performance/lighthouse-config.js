const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');

// Lighthouse配置
const config = {
  extends: 'lighthouse:default',
  settings: {
    onlyAudits: [
      'first-contentful-paint',
      'largest-contentful-paint',
      'first-meaningful-paint',
      'speed-index',
      'interactive',
      'cumulative-layout-shift',
      'total-blocking-time',
      'max-potential-fid',
      'server-response-time',
      'render-blocking-resources',
      'unused-css-rules',
      'unused-javascript',
      'modern-image-formats',
      'uses-optimized-images',
      'uses-text-compression',
      'uses-responsive-images',
      'efficient-animated-content',
      'preload-lcp-image',
      'uses-rel-preconnect',
      'uses-rel-preload',
      'critical-request-chains',
      'user-timings',
      'bootup-time',
      'mainthread-work-breakdown',
      'dom-size',
      'font-display',
      'third-party-summary',
      'third-party-facades',
      'lcp-lazy-loaded',
      'layout-shift-elements',
      'uses-passive-event-listeners',
      'no-document-write',
      'uses-http2',
      'uses-long-cache-ttl',
      'total-byte-weight',
      'offscreen-images',
      'unminified-css',
      'unminified-javascript',
      'duplicated-javascript',
      'legacy-javascript',
    ],
  },
};

// 测试页面列表
const pages = [
  {
    name: 'Home',
    url: 'http://localhost:3000/',
    description: '首页性能测试',
  },
  {
    name: 'Login',
    url: 'http://localhost:3000/login',
    description: '登录页面性能测试',
  },
  {
    name: 'Register',
    url: 'http://localhost:3000/register',
    description: '注册页面性能测试',
  },
  {
    name: 'Generation',
    url: 'http://localhost:3000/generation',
    description: '生成页面性能测试（需要登录）',
    requiresAuth: true,
  },
  {
    name: 'History',
    url: 'http://localhost:3000/history',
    description: '历史页面性能测试（需要登录）',
    requiresAuth: true,
  },
];

// 性能阈值
const thresholds = {
  'first-contentful-paint': 2000,
  'largest-contentful-paint': 4000,
  'first-meaningful-paint': 2000,
  'speed-index': 4000,
  'interactive': 5000,
  'cumulative-layout-shift': 0.1,
  'total-blocking-time': 300,
  'max-potential-fid': 130,
};

// 运行Lighthouse测试
async function runLighthouse(url, options = {}) {
  const chrome = await chromeLauncher.launch({
    chromeFlags: ['--headless', '--no-sandbox', '--disable-dev-shm-usage'],
  });

  const lighthouseOptions = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance'],
    port: chrome.port,
    ...options,
  };

  const runnerResult = await lighthouse(url, lighthouseOptions, config);
  await chrome.kill();

  return runnerResult;
}

// 分析结果
function analyzeResults(results, pageName) {
  const audits = results.audits;
  const scores = {};
  const issues = [];

  // 提取关键指标
  Object.keys(thresholds).forEach(metric => {
    if (audits[metric]) {
      const audit = audits[metric];
      scores[metric] = {
        value: audit.numericValue || audit.score,
        displayValue: audit.displayValue,
        score: audit.score,
        threshold: thresholds[metric],
      };

      // 检查是否超过阈值
      if (metric === 'cumulative-layout-shift') {
        if (audit.numericValue > thresholds[metric]) {
          issues.push(`${metric}: ${audit.displayValue} (threshold: ${thresholds[metric]})`);
        }
      } else if (audit.numericValue > thresholds[metric]) {
        issues.push(`${metric}: ${audit.displayValue} (threshold: ${thresholds[metric]}ms)`);
      }
    }
  });

  return {
    pageName,
    scores,
    issues,
    overallScore: results.categories.performance.score * 100,
  };
}

// 生成报告
function generateReport(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportDir = path.join(__dirname, 'reports');
  
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }

  // 生成JSON报告
  const jsonReport = {
    timestamp: new Date().toISOString(),
    results: results,
    summary: {
      totalPages: results.length,
      averageScore: results.reduce((sum, r) => sum + r.overallScore, 0) / results.length,
      totalIssues: results.reduce((sum, r) => sum + r.issues.length, 0),
    },
  };

  fs.writeFileSync(
    path.join(reportDir, `lighthouse-report-${timestamp}.json`),
    JSON.stringify(jsonReport, null, 2)
  );

  // 生成HTML报告
  const htmlReport = generateHtmlReport(jsonReport);
  fs.writeFileSync(
    path.join(reportDir, `lighthouse-report-${timestamp}.html`),
    htmlReport
  );

  console.log(`Reports generated in ${reportDir}`);
  return jsonReport;
}

// 生成HTML报告
function generateHtmlReport(data) {
  return `
<!DOCTYPE html>
<html>
<head>
    <title>Lighthouse Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .page-result { margin: 20px 0; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .score { font-size: 24px; font-weight: bold; }
        .score.good { color: #0cce6b; }
        .score.average { color: #ffa400; }
        .score.poor { color: #ff4e42; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 15px 0; }
        .metric { padding: 10px; background: #f9f9f9; border-radius: 3px; }
        .issues { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 3px; }
        .issue { color: #856404; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Lighthouse Performance Report</h1>
        <p>Generated: ${data.timestamp}</p>
        <div class="summary">
            <p>Total Pages: ${data.summary.totalPages}</p>
            <p>Average Score: <span class="score ${getScoreClass(data.summary.averageScore)}">${data.summary.averageScore.toFixed(1)}</span></p>
            <p>Total Issues: ${data.summary.totalIssues}</p>
        </div>
    </div>

    ${data.results.map(result => `
        <div class="page-result">
            <h2>${result.pageName}</h2>
            <p>Overall Score: <span class="score ${getScoreClass(result.overallScore)}">${result.overallScore.toFixed(1)}</span></p>
            
            <div class="metrics">
                ${Object.entries(result.scores).map(([metric, data]) => `
                    <div class="metric">
                        <strong>${metric.replace(/-/g, ' ').toUpperCase()}</strong><br>
                        ${data.displayValue}<br>
                        <small>Score: ${(data.score * 100).toFixed(1)}</small>
                    </div>
                `).join('')}
            </div>

            ${result.issues.length > 0 ? `
                <div class="issues">
                    <h3>Issues Found:</h3>
                    ${result.issues.map(issue => `<div class="issue">⚠️ ${issue}</div>`).join('')}
                </div>
            ` : '<p>✅ No performance issues found!</p>'}
        </div>
    `).join('')}
</body>
</html>
  `;
}

function getScoreClass(score) {
  if (score >= 90) return 'good';
  if (score >= 50) return 'average';
  return 'poor';
}

// 主函数
async function main() {
  console.log('Starting Lighthouse performance tests...');
  
  const results = [];

  for (const page of pages) {
    console.log(`Testing ${page.name}: ${page.url}`);
    
    try {
      const lighthouseResult = await runLighthouse(page.url);
      const analysis = analyzeResults(lighthouseResult.lhr, page.name);
      results.push(analysis);
      
      console.log(`✅ ${page.name}: Score ${analysis.overallScore.toFixed(1)}`);
      if (analysis.issues.length > 0) {
        console.log(`⚠️  Issues found: ${analysis.issues.length}`);
      }
    } catch (error) {
      console.error(`❌ Failed to test ${page.name}:`, error.message);
      results.push({
        pageName: page.name,
        scores: {},
        issues: [`Test failed: ${error.message}`],
        overallScore: 0,
      });
    }
  }

  // 生成报告
  const report = generateReport(results);
  
  console.log('\n📊 Performance Test Summary:');
  console.log(`Average Score: ${report.summary.averageScore.toFixed(1)}`);
  console.log(`Total Issues: ${report.summary.totalIssues}`);
  
  // 如果有严重问题，退出码为1
  const hasSerious Issues = results.some(r => r.overallScore < 50 || r.issues.length > 3);
  if (hasSeriousIssues) {
    console.log('❌ Performance tests failed due to serious issues');
    process.exit(1);
  } else {
    console.log('✅ Performance tests passed');
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('Performance test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runLighthouse,
  analyzeResults,
  generateReport,
  main,
};
