# 部署指南

本文档详细介绍了 Coze Clone Platform 的部署流程和配置说明。

## 🚀 部署方式

### 1. Docker 部署（推荐）

#### 开发环境部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd coze-clone

# 2. 配置环境变量
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 3. 启动服务
docker-compose up -d

# 4. 初始化数据库
docker-compose exec backend npm run db:migrate
docker-compose exec backend npm run db:seed
```

#### 生产环境部署

```bash
# 1. 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 2. 启动生产服务
docker-compose -f docker-compose.prod.yml up -d

# 3. 检查服务状态
docker-compose -f docker-compose.prod.yml ps
```

### 2. 手动部署

#### 系统要求

- Ubuntu 20.04+ / CentOS 8+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Nginx 1.18+

#### 安装步骤

```bash
# 1. 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 2. 安装 PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# 3. 安装 Redis
sudo apt-get install -y redis-server

# 4. 安装 Nginx
sudo apt-get install -y nginx

# 5. 克隆项目
git clone <repository-url>
cd coze-clone

# 6. 安装依赖
cd backend && npm install
cd ../frontend && npm install

# 7. 构建前端
cd frontend && npm run build

# 8. 配置数据库
sudo -u postgres createdb coze_clone
cd backend && npm run db:migrate && npm run db:seed

# 9. 启动服务
cd backend && npm run start
```

## ⚙️ 配置说明

### 环境变量配置

#### 后端环境变量 (.env)

```env
# 应用配置
NODE_ENV=production
PORT=8000
FRONTEND_URL=https://your-domain.com
CORS_ORIGIN=https://your-domain.com

# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/coze_clone"
REDIS_URL="redis://localhost:6379"

# JWT 配置
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters"
JWT_EXPIRES_IN="24h"

# MCP Chrome 配置
MCP_CHROME_HOST="127.0.0.1"
MCP_CHROME_PORT="12306"
MCP_CHROME_TIMEOUT="30000"

# Coze 平台配置
COZE_BASE_URL="https://space.coze.cn"
COZE_API_TIMEOUT="30000"

# 日志配置
LOG_LEVEL="info"
LOG_FILE_PATH="./logs"

# 安全配置
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="100"

# 缓存配置
CACHE_TTL="3600"
CACHE_MAX_SIZE="1000"

# 文件上传配置
MAX_FILE_SIZE="10485760"
UPLOAD_PATH="./uploads"
```

#### 前端环境变量 (.env)

```env
# API 配置
VITE_API_BASE_URL=https://api.your-domain.com
VITE_WS_URL=https://api.your-domain.com

# 应用配置
VITE_APP_NAME="Coze Clone Platform"
VITE_APP_VERSION="1.0.0"
VITE_APP_DESCRIPTION="AI Content Generation Platform"

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
```

### Nginx 配置

创建 `/etc/nginx/sites-available/coze-clone`：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 前端静态文件
    location / {
        root /var/www/coze-clone/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API 代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # WebSocket 代理
    location /socket.io/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/coze-clone /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 系统服务配置

创建 systemd 服务文件 `/etc/systemd/system/coze-clone.service`：

```ini
[Unit]
Description=Coze Clone Platform Backend
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/coze-clone/backend
Environment=NODE_ENV=production
ExecStart=/usr/bin/node dist/index.js
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=coze-clone

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable coze-clone
sudo systemctl start coze-clone
sudo systemctl status coze-clone
```

## 🔧 数据库配置

### PostgreSQL 配置

```sql
-- 创建数据库用户
CREATE USER coze_user WITH PASSWORD 'secure_password';

-- 创建数据库
CREATE DATABASE coze_clone OWNER coze_user;

-- 授权
GRANT ALL PRIVILEGES ON DATABASE coze_clone TO coze_user;
```

### Redis 配置

编辑 `/etc/redis/redis.conf`：

```conf
# 绑定地址
bind 127.0.0.1

# 端口
port 6379

# 密码保护
requirepass your_redis_password

# 持久化
save 900 1
save 300 10
save 60 10000

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## 📊 监控和日志

### 日志配置

应用日志位置：
- 应用日志: `/var/log/coze-clone/app.log`
- 错误日志: `/var/log/coze-clone/error.log`
- 访问日志: `/var/log/nginx/access.log`

### 健康检查

```bash
# 检查应用状态
curl http://localhost:8000/api/health

# 检查数据库连接
curl http://localhost:8000/api/health/database

# 检查 Redis 连接
curl http://localhost:8000/api/health/redis
```

### 性能监控

使用 PM2 进行进程管理：

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 监控
pm2 monit

# 查看日志
pm2 logs
```

## 🔒 安全配置

### SSL/TLS 配置

使用 Let's Encrypt 获取免费 SSL 证书：

```bash
# 安装 Certbot
sudo apt-get install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 防火墙配置

```bash
# 配置 UFW
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 备份策略

```bash
#!/bin/bash
# backup.sh

# 数据库备份
pg_dump -U coze_user coze_clone > /backup/db_$(date +%Y%m%d_%H%M%S).sql

# 文件备份
tar -czf /backup/files_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/coze-clone/uploads

# 清理旧备份（保留7天）
find /backup -name "*.sql" -mtime +7 -delete
find /backup -name "*.tar.gz" -mtime +7 -delete
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   
   # 检查日志
   sudo journalctl -u coze-clone -f
   ```

2. **数据库连接失败**
   ```bash
   # 检查 PostgreSQL 状态
   sudo systemctl status postgresql
   
   # 测试连接
   psql -U coze_user -d coze_clone -h localhost
   ```

3. **Redis 连接失败**
   ```bash
   # 检查 Redis 状态
   sudo systemctl status redis
   
   # 测试连接
   redis-cli ping
   ```

### 性能优化

1. **数据库优化**
   ```sql
   -- 创建索引
   CREATE INDEX CONCURRENTLY idx_generations_user_id ON generations(user_id);
   CREATE INDEX CONCURRENTLY idx_generations_created_at ON generations(created_at);
   
   -- 分析表
   ANALYZE generations;
   ```

2. **缓存优化**
   ```bash
   # Redis 内存优化
   redis-cli CONFIG SET maxmemory-policy allkeys-lru
   ```

3. **Nginx 优化**
   ```nginx
   # 启用 gzip 压缩
   gzip on;
   gzip_vary on;
   gzip_min_length 1024;
   gzip_types text/plain text/css application/json application/javascript;
   ```

## 📈 扩展部署

### 负载均衡

使用多个后端实例：

```nginx
upstream backend {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    location /api/ {
        proxy_pass http://backend;
    }
}
```

### 数据库集群

配置 PostgreSQL 主从复制：

```bash
# 主库配置
echo "wal_level = replica" >> /etc/postgresql/14/main/postgresql.conf
echo "max_wal_senders = 3" >> /etc/postgresql/14/main/postgresql.conf

# 从库配置
pg_basebackup -h master_ip -D /var/lib/postgresql/14/main -U replication -v -P
```

---

更多详细信息请参考项目文档或联系技术支持团队。
