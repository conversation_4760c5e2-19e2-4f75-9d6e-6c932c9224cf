version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: coze-clone-postgres
    environment:
      POSTGRES_DB: coze_clone
      POSTGRES_USER: coze_user
      POSTGRES_PASSWORD: coze_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - coze-clone-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: coze-clone-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - coze-clone-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: coze-clone-backend
    environment:
      NODE_ENV: production
      DATABASE_URL: **************************************************/coze_clone
      REDIS_URL: redis://redis:6379
      PORT: 8000
      MCP_CHROME_PORT: 12306
      MCP_CHROME_HOST: host.docker.internal
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend/logs:/app/logs
    networks:
      - coze-clone-network
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: coze-clone-frontend
    environment:
      REACT_APP_API_URL: http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - coze-clone-network
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: coze-clone-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - coze-clone-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  coze-clone-network:
    driver: bridge
