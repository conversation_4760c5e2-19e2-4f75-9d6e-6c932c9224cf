# 数据库配置
DATABASE_URL="postgresql://coze_user:coze_password@localhost:5432/coze_clone"
REDIS_URL="redis://localhost:6379"

# 服务配置
PORT=8000
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# MCP Chrome 配置
MCP_CHROME_PORT=12306
MCP_CHROME_HOST=127.0.0.1
MCP_CHROME_TIMEOUT=30000

# Coze 平台配置
COZE_BASE_URL="https://space.coze.cn"
COZE_API_TIMEOUT=60000

# JWT 配置
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
