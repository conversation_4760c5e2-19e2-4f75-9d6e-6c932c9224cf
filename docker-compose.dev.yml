version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: coze-clone-postgres-dev
    environment:
      POSTGRES_DB: coze_clone
      POSTGRES_USER: coze_user
      POSTGRES_PASSWORD: coze_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - coze-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: coze-clone-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - coze-network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  postgres_data_dev:
  redis_data_dev:

networks:
  coze-network:
    driver: bridge
