import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('开始数据库种子数据初始化...');

  // 创建测试用户
  const hashedPassword = await bcrypt.hash('Test123456', 12);
  
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser',
    },
  });

  console.log('创建测试用户:', testUser);

  // 创建用户设置
  const userSettings = await prisma.userSettings.upsert({
    where: { userId: testUser.id },
    update: {},
    create: {
      userId: testUser.id,
      theme: 'light',
      language: 'zh-CN',
      emailNotifications: true,
      pushNotifications: true,
      soundNotifications: false,
      defaultCategory: 'article',
      defaultTemperature: 0.7,
      defaultMaxTokens: 2000,
      autoSave: true,
    },
  });

  console.log('创建用户设置:', userSettings);

  // 创建生成模板
  const templates = [
    {
      id: 'template_article',
      name: '文章写作',
      description: '帮助用户写作各类文章，包括新闻、博客、学术论文等',
      category: 'article',
      prompt: `请帮我写一篇关于{topic}的文章，要求：
1. 结构清晰，逻辑性强
2. 内容丰富，有深度
3. 语言流畅，易于理解
4. 字数控制在{word_count}字左右

文章主题：{topic}
写作风格：{style}
目标读者：{audience}`,
      isPublic: true,
    },
    {
      id: 'template_code',
      name: '代码生成',
      description: '生成各种编程语言的代码，包括函数、类、算法等',
      category: 'code',
      prompt: `请帮我生成{language}代码来实现以下功能：

功能描述：{description}
编程语言：{language}
代码风格：{style}

要求：
1. 代码规范，注释清晰
2. 考虑错误处理和边界情况
3. 性能优化
4. 提供使用示例和测试用例`,
      isPublic: true,
    },
    {
      id: 'template_email',
      name: '邮件撰写',
      description: '撰写各类商务邮件、通知邮件等',
      category: 'email',
      prompt: `请帮我撰写一封{email_type}邮件：

收件人：{recipient}
邮件主题：{subject}
主要内容：{content}
语气要求：{tone}

要求：
1. 格式规范，符合商务邮件标准
2. 表达清晰，重点突出
3. 语气得体，符合场合
4. 包含适当的开头和结尾`,
      isPublic: true,
    },
    {
      id: 'template_summary',
      name: '内容总结',
      description: '总结和提炼长文本内容，提取关键信息',
      category: 'summary',
      prompt: `请帮我总结以下内容：

{content}

总结要求：
1. 提取关键信息和要点
2. 保持逻辑结构清晰
3. 字数控制在{word_limit}字以内
4. 突出重点，去除冗余信息
5. 使用{format}格式输出`,
      isPublic: true,
    },
    {
      id: 'template_translation',
      name: '翻译服务',
      description: '多语言翻译服务，支持各种语言对',
      category: 'translation',
      prompt: `请将以下{source_language}文本翻译成{target_language}：

原文：
{text}

翻译要求：
1. 准确传达原意，不遗漏重要信息
2. 符合目标语言的表达习惯
3. 保持原文的语气和风格
4. 注意专业术语的准确翻译
5. 如有歧义，提供多种翻译选择`,
      isPublic: true,
    },
    {
      id: 'template_story',
      name: '故事创作',
      description: '创作各类故事，包括小说、童话、科幻等',
      category: 'story',
      prompt: `请帮我创作一个{genre}故事：

故事主题：{theme}
故事类型：{genre}
主要角色：{characters}
故事背景：{setting}
字数要求：{word_count}字左右

要求：
1. 情节引人入胜，有起承转合
2. 人物形象鲜明，性格突出
3. 语言生动，富有感染力
4. 符合{genre}类型的特点
5. 结局完整，有一定深度`,
      isPublic: true,
    },
  ];

  for (const template of templates) {
    const createdTemplate = await prisma.generationTemplate.upsert({
      where: { id: template.id },
      update: {},
      create: template,
    });
    console.log('创建模板:', createdTemplate.name);
  }

  // 创建示例生成记录
  const sampleGenerations = [
    {
      userId: testUser.id,
      prompt: '请写一篇关于人工智能发展趋势的文章',
      content: `# 人工智能发展趋势：塑造未来的技术革命

## 引言

人工智能（AI）作为21世纪最具革命性的技术之一，正在以前所未有的速度改变着我们的世界。从智能手机中的语音助手到自动驾驶汽车，从医疗诊断到金融分析，AI技术已经渗透到我们生活的方方面面。

## 当前发展现状

### 技术突破
近年来，深度学习、自然语言处理、计算机视觉等领域取得了重大突破。大型语言模型如GPT系列的出现，标志着AI在理解和生成人类语言方面达到了新的高度。

### 应用领域扩展
AI应用已从传统的搜索和推荐系统扩展到更多领域：
- 医疗健康：疾病诊断、药物研发
- 教育：个性化学习、智能辅导
- 金融：风险评估、算法交易
- 制造业：智能制造、质量控制

## 未来发展趋势

### 1. 通用人工智能（AGI）
虽然距离真正的AGI还有很长的路要走，但研究者们正在朝着这个目标努力。AGI将具备人类级别的认知能力，能够在各种任务中表现出色。

### 2. 边缘AI
随着硬件技术的进步，AI计算将更多地在边缘设备上进行，减少对云端的依赖，提高响应速度和数据隐私保护。

### 3. 可解释AI
为了增强AI系统的可信度，可解释AI将成为重要发展方向，让人们能够理解AI的决策过程。

## 挑战与机遇

### 挑战
- 数据隐私和安全
- 算法偏见和公平性
- 就业影响和社会适应
- 监管和伦理问题

### 机遇
- 提高生产效率
- 解决复杂社会问题
- 创造新的就业机会
- 推动科学研究进步

## 结论

人工智能的发展将继续加速，为人类社会带来巨大的变革。我们需要在拥抱技术进步的同时，积极应对挑战，确保AI技术能够造福全人类。`,
      category: 'article',
      status: 'completed',
      metadata: {
        model: 'gpt-4',
        tokens: 1500,
        duration: 15000,
        temperature: 0.7,
      },
      completedAt: new Date(),
    },
    {
      userId: testUser.id,
      prompt: '请生成一个Python函数来计算斐波那契数列',
      content: `\`\`\`python
def fibonacci(n):
    """
    计算斐波那契数列的第n项
    
    Args:
        n (int): 要计算的项数（从0开始）
    
    Returns:
        int: 斐波那契数列的第n项
    
    Raises:
        ValueError: 当n为负数时抛出异常
    """
    if n < 0:
        raise ValueError("n必须是非负整数")
    
    if n <= 1:
        return n
    
    # 使用动态规划方法，时间复杂度O(n)，空间复杂度O(1)
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    
    return b

def fibonacci_sequence(count):
    """
    生成斐波那契数列的前count项
    
    Args:
        count (int): 要生成的项数
    
    Returns:
        list: 包含前count项斐波那契数的列表
    """
    if count <= 0:
        return []
    
    sequence = []
    for i in range(count):
        sequence.append(fibonacci(i))
    
    return sequence

# 使用示例
if __name__ == "__main__":
    # 计算第10项斐波那契数
    print(f"第10项斐波那契数: {fibonacci(10)}")
    
    # 生成前15项斐波那契数列
    print(f"前15项斐波那契数列: {fibonacci_sequence(15)}")
    
    # 测试边界情况
    print(f"第0项: {fibonacci(0)}")
    print(f"第1项: {fibonacci(1)}")
\`\`\`

这个实现包含了：
1. 主函数 \`fibonacci(n)\` 用于计算第n项
2. 辅助函数 \`fibonacci_sequence(count)\` 用于生成数列
3. 完整的错误处理
4. 详细的文档字符串
5. 使用示例和测试用例
6. 优化的算法（动态规划，避免重复计算）`,
      category: 'code',
      status: 'completed',
      metadata: {
        model: 'gpt-4',
        tokens: 800,
        duration: 8000,
        temperature: 0.3,
        language: 'python',
      },
      completedAt: new Date(),
    },
  ];

  for (const generation of sampleGenerations) {
    const created = await prisma.generation.create({
      data: generation,
    });
    console.log('创建示例生成记录:', created.id);
  }

  console.log('数据库种子数据初始化完成！');
}

main()
  .catch((e) => {
    console.error('种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
