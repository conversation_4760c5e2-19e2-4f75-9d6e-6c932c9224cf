// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String
  avatar    String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  generations Generation[]
  settings    UserSettings?
  sessions    UserSession[]

  @@map("users")
}

model UserSettings {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // 主题设置
  theme    String @default("light") // light, dark, auto
  language String @default("zh-CN") // zh-CN, en-US

  // 通知设置
  emailNotifications <PERSON>ole<PERSON> @default(true)
  pushNotifications  Boolean @default(true)
  soundNotifications <PERSON><PERSON><PERSON> @default(true)

  // 生成设置
  defaultCategory    String?
  defaultModel       String?
  defaultTemperature Float?  @default(0.7)
  defaultMaxTokens   Int?    @default(2000)

  // 其他设置
  autoSave Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("user_settings")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  refreshToken String   @unique
  userAgent    String?
  ipAddress    String?
  expiresAt    DateTime
  createdAt    DateTime @default(now())

  @@map("user_sessions")
}

model Generation {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  prompt    String
  content   String?
  images    Json?    // Array of image URLs
  metadata  Json?    // Additional metadata
  category  String?
  status    String   @default("pending") // pending, processing, completed, failed
  error     String?
  options   Json?    // Generation options
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  completedAt DateTime?

  // 用户交互
  isFavorite Boolean @default(false)
  tags       String[]
  notes      String?

  @@map("generations")
}

model GenerationTemplate {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String
  prompt      String
  options     Json?
  isPublic    Boolean  @default(false)
  createdBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("generation_templates")
}

model SystemLog {
  id        String   @id @default(cuid())
  level     String   // error, warn, info, debug
  message   String
  details   Json?
  source    String?  // service name or component
  userId    String?
  createdAt DateTime @default(now())

  @@map("system_logs")
}

model ApiUsage {
  id        String   @id @default(cuid())
  userId    String?
  endpoint  String
  method    String
  statusCode Int
  responseTime Int
  userAgent String?
  ipAddress String?
  createdAt DateTime @default(now())

  @@map("api_usage")
}


