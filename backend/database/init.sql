-- 初始化数据库脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS coze_clone;

-- 使用数据库
\c coze_clone;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建索引（Prisma会自动创建表，这里只创建额外的索引）

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 生成记录表索引
CREATE INDEX IF NOT EXISTS idx_generations_user_id_created_at ON generations(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_generations_status ON generations(status);
CREATE INDEX IF NOT EXISTS idx_generations_category ON generations(category);
CREATE INDEX IF NOT EXISTS idx_generations_is_favorite ON generations(is_favorite);
CREATE INDEX IF NOT EXISTS idx_generations_content_gin ON generations USING gin(to_tsvector('english', content));
CREATE INDEX IF NOT EXISTS idx_generations_prompt_gin ON generations USING gin(to_tsvector('english', prompt));

-- 用户会话表索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_refresh_token ON user_sessions(refresh_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- 系统日志表索引
CREATE INDEX IF NOT EXISTS idx_system_logs_level_created_at ON system_logs(level, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_logs_source_created_at ON system_logs(source, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id);

-- API使用记录表索引
CREATE INDEX IF NOT EXISTS idx_api_usage_user_id_created_at ON api_usage(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint_created_at ON api_usage(endpoint, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_usage_status_code ON api_usage(status_code);

-- 生成模板表索引
CREATE INDEX IF NOT EXISTS idx_generation_templates_category ON generation_templates(category);
CREATE INDEX IF NOT EXISTS idx_generation_templates_is_public ON generation_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_generation_templates_created_by ON generation_templates(created_by);

-- 创建函数：更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generations_updated_at BEFORE UPDATE ON generations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_generation_templates_updated_at BEFORE UPDATE ON generation_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建视图：用户统计
CREATE OR REPLACE VIEW user_stats AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at as user_created_at,
    COUNT(g.id) as total_generations,
    COUNT(CASE WHEN g.status = 'completed' THEN 1 END) as completed_generations,
    COUNT(CASE WHEN g.status = 'failed' THEN 1 END) as failed_generations,
    COUNT(CASE WHEN g.is_favorite = true THEN 1 END) as favorite_generations,
    MAX(g.created_at) as last_generation_at
FROM users u
LEFT JOIN generations g ON u.id = g.user_id
GROUP BY u.id, u.username, u.email, u.created_at;

-- 创建视图：每日统计
CREATE OR REPLACE VIEW daily_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_generations,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_generations,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_generations,
    COUNT(DISTINCT user_id) as active_users,
    AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_duration_seconds
FROM generations
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 创建函数：清理过期数据
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- 清理过期的用户会话
    DELETE FROM user_sessions 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    -- 清理30天前的系统日志
    DELETE FROM system_logs 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    -- 清理90天前的API使用记录
    DELETE FROM api_usage 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
    
    RAISE NOTICE 'Expired data cleanup completed';
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务（需要pg_cron扩展，可选）
-- SELECT cron.schedule('cleanup-expired-data', '0 2 * * *', 'SELECT cleanup_expired_data();');

-- 插入默认数据
INSERT INTO generation_templates (id, name, description, category, prompt, is_public, created_at, updated_at)
VALUES 
    ('template_1', '文章写作', '帮助用户写作各类文章', 'article', '请帮我写一篇关于{topic}的文章，要求：\n1. 结构清晰，逻辑性强\n2. 内容丰富，有深度\n3. 语言流畅，易于理解\n\n文章主题：{topic}\n字数要求：{word_count}字左右', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('template_2', '代码生成', '生成各种编程语言的代码', 'code', '请帮我生成{language}代码来实现以下功能：\n\n功能描述：{description}\n\n要求：\n1. 代码规范，注释清晰\n2. 考虑错误处理\n3. 性能优化\n4. 提供使用示例', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('template_3', '邮件撰写', '撰写各类商务邮件', 'email', '请帮我撰写一封{email_type}邮件：\n\n收件人：{recipient}\n主题：{subject}\n主要内容：{content}\n\n要求：\n1. 语气{tone}\n2. 格式规范\n3. 表达清晰', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('template_4', '内容总结', '总结和提炼文本内容', 'summary', '请帮我总结以下内容：\n\n{content}\n\n总结要求：\n1. 提取关键信息\n2. 保持逻辑结构\n3. 字数控制在{word_limit}字以内\n4. 突出重点', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('template_5', '翻译服务', '多语言翻译服务', 'translation', '请将以下{source_language}文本翻译成{target_language}：\n\n{text}\n\n翻译要求：\n1. 准确传达原意\n2. 符合目标语言习惯\n3. 保持原文风格\n4. 注意专业术语', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 创建性能监控函数
CREATE OR REPLACE FUNCTION get_performance_stats()
RETURNS TABLE(
    metric_name text,
    metric_value numeric,
    metric_unit text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'total_users'::text,
        COUNT(*)::numeric,
        'count'::text
    FROM users
    UNION ALL
    SELECT 
        'active_users_today'::text,
        COUNT(DISTINCT user_id)::numeric,
        'count'::text
    FROM generations
    WHERE created_at >= CURRENT_DATE
    UNION ALL
    SELECT 
        'total_generations'::text,
        COUNT(*)::numeric,
        'count'::text
    FROM generations
    UNION ALL
    SELECT 
        'avg_generation_time'::text,
        AVG(EXTRACT(EPOCH FROM (completed_at - created_at)))::numeric,
        'seconds'::text
    FROM generations
    WHERE status = 'completed' AND completed_at IS NOT NULL
    UNION ALL
    SELECT 
        'success_rate'::text,
        (COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*))::numeric,
        'percentage'::text
    FROM generations;
END;
$$ LANGUAGE plpgsql;

-- 创建备份函数
CREATE OR REPLACE FUNCTION backup_user_data(user_id_param text)
RETURNS json AS $$
DECLARE
    result json;
BEGIN
    SELECT json_build_object(
        'user', (
            SELECT row_to_json(u)
            FROM (
                SELECT id, username, email, avatar, created_at, updated_at
                FROM users
                WHERE id = user_id_param
            ) u
        ),
        'settings', (
            SELECT row_to_json(s)
            FROM user_settings s
            WHERE s.user_id = user_id_param
        ),
        'generations', (
            SELECT json_agg(row_to_json(g))
            FROM (
                SELECT id, prompt, content, images, metadata, category, status, 
                       is_favorite, tags, notes, created_at, completed_at
                FROM generations
                WHERE user_id = user_id_param
                ORDER BY created_at DESC
            ) g
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;
