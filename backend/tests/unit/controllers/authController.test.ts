import request from 'supertest';
import { Express } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { createTestUser, createAuthToken } from '../../setup';
import { AuthController } from '@/controllers/authController';

// Mock Express app for testing
const createMockApp = (): Express => {
  const express = require('express');
  const app = express();
  app.use(express.json());
  
  const authController = new AuthController();
  
  // 注册路由
  app.post('/register', authController.register.bind(authController));
  app.post('/login', authController.login.bind(authController));
  app.post('/logout', authController.logout.bind(authController));
  app.post('/refresh', authController.refreshToken.bind(authController));
  app.get('/me', (req: any, res: any, next: any) => {
    // 模拟认证中间件
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
        req.user = decoded;
      } catch (error) {
        // Token无效
      }
    }
    next();
  }, authController.getCurrentUser.bind(authController));
  
  return app;
};

describe('AuthController', () => {
  let app: Express;
  let prisma: any;

  beforeAll(() => {
    app = createMockApp();
    prisma = global.__PRISMA__;
  });

  describe('POST /register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'Test123456',
        confirmPassword: 'Test123456',
      };

      const response = await request(app)
        .post('/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        username: userData.username,
        email: userData.email,
      });
      expect(response.body.data.user.password).toBeUndefined();
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();

      // 验证用户已保存到数据库
      const savedUser = await prisma.user.findUnique({
        where: { email: userData.email },
      });
      expect(savedUser).toBeTruthy();
      expect(savedUser.username).toBe(userData.username);
    });

    it('should hash the password correctly', async () => {
      const userData = {
        username: 'testuser2',
        email: '<EMAIL>',
        password: 'Test123456',
        confirmPassword: 'Test123456',
      };

      await request(app)
        .post('/register')
        .send(userData)
        .expect(201);

      const savedUser = await prisma.user.findUnique({
        where: { email: userData.email },
      });

      expect(savedUser.password).not.toBe(userData.password);
      const isPasswordValid = await bcrypt.compare(userData.password, savedUser.password);
      expect(isPasswordValid).toBe(true);
    });

    it('should create default user settings', async () => {
      const userData = {
        username: 'testuser3',
        email: '<EMAIL>',
        password: 'Test123456',
        confirmPassword: 'Test123456',
      };

      const response = await request(app)
        .post('/register')
        .send(userData)
        .expect(201);

      const userSettings = await prisma.userSettings.findUnique({
        where: { userId: response.body.data.user.id },
      });

      expect(userSettings).toBeTruthy();
    });

    it('should return 400 for invalid input', async () => {
      const invalidData = {
        username: 'ab', // 太短
        email: 'invalid-email',
        password: '123', // 太短
        confirmPassword: '456', // 不匹配
      };

      const response = await request(app)
        .post('/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('VALIDATION_ERROR');
    });

    it('should return 409 for duplicate email', async () => {
      const userData = {
        username: 'user1',
        email: '<EMAIL>',
        password: 'Test123456',
        confirmPassword: 'Test123456',
      };

      // 第一次注册
      await request(app)
        .post('/register')
        .send(userData)
        .expect(201);

      // 第二次注册相同邮箱
      const duplicateData = {
        ...userData,
        username: 'user2',
      };

      const response = await request(app)
        .post('/register')
        .send(duplicateData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Email already registered');
    });

    it('should return 409 for duplicate username', async () => {
      const userData = {
        username: 'duplicateuser',
        email: '<EMAIL>',
        password: 'Test123456',
        confirmPassword: 'Test123456',
      };

      // 第一次注册
      await request(app)
        .post('/register')
        .send(userData)
        .expect(201);

      // 第二次注册相同用户名
      const duplicateData = {
        ...userData,
        email: '<EMAIL>',
      };

      const response = await request(app)
        .post('/register')
        .send(duplicateData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Username already taken');
    });
  });

  describe('POST /login', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = await createTestUser({
        username: 'loginuser',
        email: '<EMAIL>',
      });
    });

    it('should login successfully with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Test123456',
      };

      const response = await request(app)
        .post('/login')
        .send(loginData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        id: testUser.id,
        username: testUser.username,
        email: testUser.email,
      });
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
    });

    it('should return 401 for invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Test123456',
      };

      const response = await request(app)
        .post('/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid email or password');
    });

    it('should return 401 for invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword',
      };

      const response = await request(app)
        .post('/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid email or password');
    });

    it('should return 401 for inactive user', async () => {
      // 创建非活跃用户
      const inactiveUser = await createTestUser({
        username: 'inactiveuser',
        email: '<EMAIL>',
        isActive: false,
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'Test123456',
      };

      const response = await request(app)
        .post('/login')
        .send(loginData)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Account is disabled');
    });

    it('should create user session on successful login', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Test123456',
      };

      const response = await request(app)
        .post('/login')
        .send(loginData)
        .expect(200);

      const session = await prisma.userSession.findFirst({
        where: { userId: testUser.id },
      });

      expect(session).toBeTruthy();
      expect(session.refreshToken).toBe(response.body.data.refreshToken);
    });

    it('should return 400 for invalid input format', async () => {
      const invalidData = {
        email: 'not-an-email',
        password: '',
      };

      const response = await request(app)
        .post('/login')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /me', () => {
    let testUser: any;
    let authToken: string;

    beforeEach(async () => {
      testUser = await createTestUser({
        username: 'meuser',
        email: '<EMAIL>',
      });
      authToken = createAuthToken(testUser);
    });

    it('should return current user info with valid token', async () => {
      const response = await request(app)
        .get('/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: testUser.id,
        username: testUser.username,
        email: testUser.email,
      });
      expect(response.body.data.password).toBeUndefined();
    });

    it('should return 401 without token', async () => {
      const response = await request(app)
        .get('/me')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should return 401 with invalid token', async () => {
      const response = await request(app)
        .get('/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('should return 401 for non-existent user', async () => {
      // 创建token但删除用户
      await prisma.user.delete({ where: { id: testUser.id } });

      const response = await request(app)
        .get('/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /refresh', () => {
    let testUser: any;
    let refreshToken: string;

    beforeEach(async () => {
      testUser = await createTestUser({
        username: 'refreshuser',
        email: '<EMAIL>',
      });

      // 创建refresh token
      const { v4: uuidv4 } = require('uuid');
      refreshToken = uuidv4();
      
      await prisma.userSession.create({
        data: {
          userId: testUser.id,
          refreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
        },
      });
    });

    it('should refresh token successfully', async () => {
      const response = await request(app)
        .post('/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user).toMatchObject({
        id: testUser.id,
        username: testUser.username,
        email: testUser.email,
      });
      expect(response.body.data.token).toBeDefined();
      expect(response.body.data.refreshToken).toBeDefined();
      expect(response.body.data.refreshToken).not.toBe(refreshToken); // 应该是新的refresh token
    });

    it('should return 401 for invalid refresh token', async () => {
      const response = await request(app)
        .post('/refresh')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid or expired refresh token');
    });

    it('should return 401 for expired refresh token', async () => {
      // 更新refresh token为已过期
      await prisma.userSession.update({
        where: { refreshToken },
        data: { expiresAt: new Date(Date.now() - 1000) }, // 1秒前过期
      });

      const response = await request(app)
        .post('/refresh')
        .send({ refreshToken })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Invalid or expired refresh token');
    });

    it('should return 401 for inactive user', async () => {
      // 将用户设为非活跃
      await prisma.user.update({
        where: { id: testUser.id },
        data: { isActive: false },
      });

      const response = await request(app)
        .post('/refresh')
        .send({ refreshToken })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Account is disabled');
    });

    it('should update session with new refresh token', async () => {
      const response = await request(app)
        .post('/refresh')
        .send({ refreshToken })
        .expect(200);

      const updatedSession = await prisma.userSession.findUnique({
        where: { refreshToken: response.body.data.refreshToken },
      });

      expect(updatedSession).toBeTruthy();
      expect(updatedSession.userId).toBe(testUser.id);

      // 旧的refresh token应该不存在
      const oldSession = await prisma.userSession.findUnique({
        where: { refreshToken },
      });
      expect(oldSession).toBeNull();
    });
  });

  describe('POST /logout', () => {
    let testUser: any;
    let authToken: string;

    beforeEach(async () => {
      testUser = await createTestUser({
        username: 'logoutuser',
        email: '<EMAIL>',
      });
      authToken = createAuthToken(testUser);

      // 创建一些session
      await prisma.userSession.createMany({
        data: [
          {
            userId: testUser.id,
            refreshToken: 'token1',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          },
          {
            userId: testUser.id,
            refreshToken: 'token2',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          },
        ],
      });
    });

    it('should logout successfully and delete all sessions', async () => {
      const response = await request(app)
        .post('/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Logout successful');

      // 验证所有session都被删除
      const sessions = await prisma.userSession.findMany({
        where: { userId: testUser.id },
      });
      expect(sessions).toHaveLength(0);
    });

    it('should logout successfully even without valid token', async () => {
      const response = await request(app)
        .post('/logout')
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should logout successfully with invalid token', async () => {
      const response = await request(app)
        .post('/logout')
        .set('Authorization', 'Bearer invalid-token')
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });
});
