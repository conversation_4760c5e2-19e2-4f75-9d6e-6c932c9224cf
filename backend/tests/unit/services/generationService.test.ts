import GenerationService from '@/services/generationService';
import { createTestUser, createTestGeneration, mockCozeAutomation } from '../../setup';
import { GenerationRequest } from '@/types';

// Mock dependencies
jest.mock('@/mcp/cozeAutomation', () => ({
  CozeAutomation: jest.fn().mockImplementation(() => mockCozeAutomation),
}));

jest.mock('@/index', () => ({
  io: {
    to: jest.fn().mockReturnThis(),
    emit: jest.fn(),
  },
}));

describe('GenerationService', () => {
  let generationService: GenerationService;
  let testUser: any;
  let prisma: any;

  beforeAll(() => {
    prisma = global.__PRISMA__;
  });

  beforeEach(async () => {
    generationService = new GenerationService();
    testUser = await createTestUser({
      username: 'genuser',
      email: '<EMAIL>',
    });
  });

  describe('createGeneration', () => {
    it('should create a new generation task successfully', async () => {
      const request: GenerationRequest = {
        prompt: 'Write a test article',
        category: 'article',
        userId: testUser.id,
        options: {
          temperature: 0.7,
          maxTokens: 2000,
          saveToHistory: true,
        },
      };

      const result = await generationService.createGeneration(request);

      expect(result).toMatchObject({
        id: expect.any(String),
        content: '',
        status: 'pending',
        createdAt: expect.any(Date),
      });

      // 验证数据库记录
      const dbGeneration = await prisma.generation.findUnique({
        where: { id: result.id },
      });

      expect(dbGeneration).toMatchObject({
        userId: testUser.id,
        prompt: request.prompt,
        category: request.category,
        status: 'pending',
        options: request.options,
      });
    });

    it('should validate required fields', async () => {
      const invalidRequest = {
        prompt: '', // 空prompt
        userId: testUser.id,
      } as GenerationRequest;

      await expect(generationService.createGeneration(invalidRequest))
        .rejects.toThrow('Prompt is required');
    });

    it('should validate prompt length', async () => {
      const longPrompt = 'a'.repeat(10001); // 超过10000字符
      const invalidRequest: GenerationRequest = {
        prompt: longPrompt,
        userId: testUser.id,
      };

      await expect(generationService.createGeneration(invalidRequest))
        .rejects.toThrow('Prompt is too long');
    });

    it('should validate user ID', async () => {
      const invalidRequest = {
        prompt: 'Test prompt',
        userId: '', // 空用户ID
      } as GenerationRequest;

      await expect(generationService.createGeneration(invalidRequest))
        .rejects.toThrow('User ID is required');
    });

    it('should start async processing', async () => {
      const request: GenerationRequest = {
        prompt: 'Test prompt',
        userId: testUser.id,
      };

      const result = await generationService.createGeneration(request);

      // 等待异步处理开始
      await new Promise(resolve => setTimeout(resolve, 100));

      // 验证Coze自动化被调用
      expect(mockCozeAutomation.generateContent).toHaveBeenCalledWith({
        prompt: request.prompt,
        category: request.category,
        options: request.options,
      });
    });
  });

  describe('getGeneration', () => {
    let testGeneration: any;

    beforeEach(async () => {
      testGeneration = await createTestGeneration(testUser.id, {
        prompt: 'Test prompt',
        content: 'Test content',
        status: 'completed',
      });
    });

    it('should get generation by ID', async () => {
      const result = await generationService.getGeneration(testGeneration.id, testUser.id);

      expect(result).toMatchObject({
        id: testGeneration.id,
        content: testGeneration.content,
        status: testGeneration.status,
        createdAt: expect.any(Date),
      });
    });

    it('should return null for non-existent generation', async () => {
      const result = await generationService.getGeneration('non-existent-id', testUser.id);
      expect(result).toBeNull();
    });

    it('should return null for generation belonging to different user', async () => {
      const otherUser = await createTestUser({
        username: 'otheruser',
        email: '<EMAIL>',
      });

      const result = await generationService.getGeneration(testGeneration.id, otherUser.id);
      expect(result).toBeNull();
    });

    it('should cache completed generations', async () => {
      const redis = global.__REDIS__;
      
      // 第一次获取
      await generationService.getGeneration(testGeneration.id, testUser.id);
      
      // 验证缓存
      const cacheKey = `generation:${testGeneration.id}`;
      const cached = await redis.get(cacheKey);
      expect(cached).toBeTruthy();
      
      const cachedData = JSON.parse(cached);
      expect(cachedData.id).toBe(testGeneration.id);
    });
  });

  describe('getUserGenerations', () => {
    beforeEach(async () => {
      // 创建多个生成记录
      await Promise.all([
        createTestGeneration(testUser.id, { prompt: 'Prompt 1', status: 'completed' }),
        createTestGeneration(testUser.id, { prompt: 'Prompt 2', status: 'pending' }),
        createTestGeneration(testUser.id, { prompt: 'Prompt 3', status: 'failed' }),
      ]);
    });

    it('should get user generations with pagination', async () => {
      const result = await generationService.getUserGenerations(testUser.id, 1, 2);

      expect(result.generations).toHaveLength(2);
      expect(result.total).toBe(3);
      expect(result.page).toBe(1);
      expect(result.totalPages).toBe(2);
    });

    it('should return generations in descending order by creation date', async () => {
      const result = await generationService.getUserGenerations(testUser.id);

      expect(result.generations).toHaveLength(3);
      
      // 验证排序（最新的在前）
      for (let i = 0; i < result.generations.length - 1; i++) {
        const current = new Date(result.generations[i].createdAt);
        const next = new Date(result.generations[i + 1].createdAt);
        expect(current.getTime()).toBeGreaterThanOrEqual(next.getTime());
      }
    });

    it('should handle empty results', async () => {
      const emptyUser = await createTestUser({
        username: 'emptyuser',
        email: '<EMAIL>',
      });

      const result = await generationService.getUserGenerations(emptyUser.id);

      expect(result.generations).toHaveLength(0);
      expect(result.total).toBe(0);
      expect(result.totalPages).toBe(0);
    });
  });

  describe('searchGenerations', () => {
    beforeEach(async () => {
      await Promise.all([
        createTestGeneration(testUser.id, { 
          prompt: 'Write about artificial intelligence',
          content: 'AI is transforming the world',
          status: 'completed' 
        }),
        createTestGeneration(testUser.id, { 
          prompt: 'Create a recipe for pasta',
          content: 'Delicious pasta recipe',
          status: 'completed' 
        }),
        createTestGeneration(testUser.id, { 
          prompt: 'Explain machine learning',
          content: 'Machine learning basics',
          status: 'completed' 
        }),
      ]);
    });

    it('should search generations by prompt content', async () => {
      const result = await generationService.searchGenerations(testUser.id, 'artificial intelligence');

      expect(result.generations).toHaveLength(1);
      expect(result.generations[0].prompt).toContain('artificial intelligence');
    });

    it('should search generations by generated content', async () => {
      const result = await generationService.searchGenerations(testUser.id, 'pasta recipe');

      expect(result.generations).toHaveLength(1);
      expect(result.generations[0].content).toContain('pasta recipe');
    });

    it('should be case insensitive', async () => {
      const result = await generationService.searchGenerations(testUser.id, 'MACHINE LEARNING');

      expect(result.generations).toHaveLength(1);
      expect(result.generations[0].prompt).toContain('machine learning');
    });

    it('should return empty results for no matches', async () => {
      const result = await generationService.searchGenerations(testUser.id, 'nonexistent content');

      expect(result.generations).toHaveLength(0);
      expect(result.total).toBe(0);
    });

    it('should support pagination in search results', async () => {
      const result = await generationService.searchGenerations(testUser.id, 'e', 1, 2);

      expect(result.generations.length).toBeLessThanOrEqual(2);
      expect(result.page).toBe(1);
    });
  });

  describe('deleteGeneration', () => {
    let testGeneration: any;

    beforeEach(async () => {
      testGeneration = await createTestGeneration(testUser.id);
    });

    it('should delete generation successfully', async () => {
      const result = await generationService.deleteGeneration(testGeneration.id, testUser.id);

      expect(result).toBe(true);

      // 验证数据库中已删除
      const deleted = await prisma.generation.findUnique({
        where: { id: testGeneration.id },
      });
      expect(deleted).toBeNull();
    });

    it('should return false for non-existent generation', async () => {
      const result = await generationService.deleteGeneration('non-existent-id', testUser.id);
      expect(result).toBe(false);
    });

    it('should return false for generation belonging to different user', async () => {
      const otherUser = await createTestUser({
        username: 'otheruser2',
        email: '<EMAIL>',
      });

      const result = await generationService.deleteGeneration(testGeneration.id, otherUser.id);
      expect(result).toBe(false);

      // 验证生成记录仍然存在
      const stillExists = await prisma.generation.findUnique({
        where: { id: testGeneration.id },
      });
      expect(stillExists).toBeTruthy();
    });

    it('should clear cache when deleting', async () => {
      const redis = global.__REDIS__;
      const cacheKey = `generation:${testGeneration.id}`;
      
      // 先设置缓存
      await redis.set(cacheKey, JSON.stringify({ id: testGeneration.id }));
      
      // 删除生成记录
      await generationService.deleteGeneration(testGeneration.id, testUser.id);
      
      // 验证缓存已清除
      const cached = await redis.get(cacheKey);
      expect(cached).toBeNull();
    });
  });

  describe('getGenerationStats', () => {
    beforeEach(async () => {
      await Promise.all([
        createTestGeneration(testUser.id, { status: 'completed' }),
        createTestGeneration(testUser.id, { status: 'completed' }),
        createTestGeneration(testUser.id, { status: 'failed' }),
        createTestGeneration(testUser.id, { status: 'pending' }),
        createTestGeneration(testUser.id, { status: 'processing' }),
      ]);
    });

    it('should return correct generation statistics', async () => {
      const stats = await generationService.getGenerationStats(testUser.id);

      expect(stats).toEqual({
        total: 5,
        completed: 2,
        failed: 1,
        pending: 1,
        processing: 1,
      });
    });

    it('should return zero stats for user with no generations', async () => {
      const emptyUser = await createTestUser({
        username: 'emptyuser2',
        email: '<EMAIL>',
      });

      const stats = await generationService.getGenerationStats(emptyUser.id);

      expect(stats).toEqual({
        total: 0,
        completed: 0,
        failed: 0,
        pending: 0,
        processing: 0,
      });
    });
  });

  describe('Error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock database error
      const originalFindUnique = prisma.generation.findUnique;
      prisma.generation.findUnique = jest.fn().mockRejectedValue(new Error('Database error'));

      await expect(generationService.getGeneration('test-id', testUser.id))
        .rejects.toThrow('Failed to get generation');

      // Restore original method
      prisma.generation.findUnique = originalFindUnique;
    });

    it('should handle Coze automation errors', async () => {
      // Mock Coze automation error
      mockCozeAutomation.generateContent.mockRejectedValueOnce(new Error('Coze API error'));

      const request: GenerationRequest = {
        prompt: 'Test prompt',
        userId: testUser.id,
      };

      const result = await generationService.createGeneration(request);

      // 等待异步处理完成
      await new Promise(resolve => setTimeout(resolve, 200));

      // 验证生成状态被更新为失败
      const updatedGeneration = await prisma.generation.findUnique({
        where: { id: result.id },
      });

      expect(updatedGeneration.status).toBe('failed');
      expect(updatedGeneration.error).toContain('Coze API error');
    });
  });
});
