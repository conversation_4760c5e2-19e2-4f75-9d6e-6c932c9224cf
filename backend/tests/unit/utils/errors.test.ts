import {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  MCPError,
  GenerationError,
  DatabaseError,
  ExternalServiceError,
} from '@/utils/errors';

describe('Error Classes', () => {
  describe('AppError', () => {
    it('should create an AppError with default values', () => {
      const error = new AppError('Test error');
      
      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(500);
      expect(error.isOperational).toBe(true);
      expect(error.code).toBeUndefined();
      expect(error.details).toBeUndefined();
    });

    it('should create an AppError with custom values', () => {
      const details = { field: 'test' };
      const error = new AppError('Custom error', 400, 'CUSTOM_ERROR', details);
      
      expect(error.message).toBe('Custom error');
      expect(error.statusCode).toBe(400);
      expect(error.code).toBe('CUSTOM_ERROR');
      expect(error.details).toBe(details);
    });

    it('should capture stack trace', () => {
      const error = new AppError('Test error');
      expect(error.stack).toBeDefined();
    });
  });

  describe('ValidationError', () => {
    it('should create a ValidationError with correct defaults', () => {
      const error = new ValidationError('Validation failed');
      
      expect(error.message).toBe('Validation failed');
      expect(error.statusCode).toBe(400);
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.isOperational).toBe(true);
    });

    it('should create a ValidationError with details', () => {
      const details = [{ field: 'email', message: 'Invalid email' }];
      const error = new ValidationError('Validation failed', details);
      
      expect(error.details).toBe(details);
    });
  });

  describe('AuthenticationError', () => {
    it('should create an AuthenticationError with default message', () => {
      const error = new AuthenticationError();
      
      expect(error.message).toBe('Authentication failed');
      expect(error.statusCode).toBe(401);
      expect(error.code).toBe('AUTHENTICATION_ERROR');
    });

    it('should create an AuthenticationError with custom message', () => {
      const error = new AuthenticationError('Invalid token');
      
      expect(error.message).toBe('Invalid token');
      expect(error.statusCode).toBe(401);
    });
  });

  describe('AuthorizationError', () => {
    it('should create an AuthorizationError with default message', () => {
      const error = new AuthorizationError();
      
      expect(error.message).toBe('Access denied');
      expect(error.statusCode).toBe(403);
      expect(error.code).toBe('AUTHORIZATION_ERROR');
    });

    it('should create an AuthorizationError with custom message', () => {
      const error = new AuthorizationError('Insufficient permissions');
      
      expect(error.message).toBe('Insufficient permissions');
    });
  });

  describe('NotFoundError', () => {
    it('should create a NotFoundError with default message', () => {
      const error = new NotFoundError();
      
      expect(error.message).toBe('Resource not found');
      expect(error.statusCode).toBe(404);
      expect(error.code).toBe('NOT_FOUND_ERROR');
    });

    it('should create a NotFoundError with custom message', () => {
      const error = new NotFoundError('User not found');
      
      expect(error.message).toBe('User not found');
    });
  });

  describe('ConflictError', () => {
    it('should create a ConflictError with default message', () => {
      const error = new ConflictError();
      
      expect(error.message).toBe('Resource conflict');
      expect(error.statusCode).toBe(409);
      expect(error.code).toBe('CONFLICT_ERROR');
    });

    it('should create a ConflictError with custom message', () => {
      const error = new ConflictError('Email already exists');
      
      expect(error.message).toBe('Email already exists');
    });
  });

  describe('RateLimitError', () => {
    it('should create a RateLimitError with default message', () => {
      const error = new RateLimitError();
      
      expect(error.message).toBe('Rate limit exceeded');
      expect(error.statusCode).toBe(429);
      expect(error.code).toBe('RATE_LIMIT_ERROR');
    });
  });

  describe('MCPError', () => {
    it('should create an MCPError with correct defaults', () => {
      const error = new MCPError('MCP connection failed');
      
      expect(error.message).toBe('MCP connection failed');
      expect(error.statusCode).toBe(500);
      expect(error.code).toBe('MCP_ERROR');
    });

    it('should create an MCPError with details', () => {
      const details = { host: 'localhost', port: 12306 };
      const error = new MCPError('Connection timeout', details);
      
      expect(error.details).toBe(details);
    });
  });

  describe('GenerationError', () => {
    it('should create a GenerationError with correct defaults', () => {
      const error = new GenerationError('Generation failed');
      
      expect(error.message).toBe('Generation failed');
      expect(error.statusCode).toBe(500);
      expect(error.code).toBe('GENERATION_ERROR');
    });
  });

  describe('DatabaseError', () => {
    it('should create a DatabaseError with correct defaults', () => {
      const error = new DatabaseError('Database connection failed');
      
      expect(error.message).toBe('Database connection failed');
      expect(error.statusCode).toBe(500);
      expect(error.code).toBe('DATABASE_ERROR');
    });
  });

  describe('ExternalServiceError', () => {
    it('should create an ExternalServiceError with service info', () => {
      const error = new ExternalServiceError('Service unavailable', 'coze-api');
      
      expect(error.message).toBe('Service unavailable');
      expect(error.statusCode).toBe(502);
      expect(error.code).toBe('EXTERNAL_SERVICE_ERROR');
      expect(error.details).toEqual({ service: 'coze-api' });
    });

    it('should create an ExternalServiceError with additional details', () => {
      const details = { timeout: 30000, retries: 3 };
      const error = new ExternalServiceError('Timeout', 'api-service', details);
      
      expect(error.details).toEqual({ service: 'api-service', timeout: 30000, retries: 3 });
    });
  });

  describe('Error inheritance', () => {
    it('should properly inherit from Error', () => {
      const error = new ValidationError('Test');
      
      expect(error instanceof Error).toBe(true);
      expect(error instanceof AppError).toBe(true);
      expect(error instanceof ValidationError).toBe(true);
    });

    it('should have correct error name', () => {
      const validationError = new ValidationError('Test');
      const authError = new AuthenticationError('Test');
      
      expect(validationError.name).toBe('ValidationError');
      expect(authError.name).toBe('AuthenticationError');
    });
  });

  describe('Error serialization', () => {
    it('should serialize error properties correctly', () => {
      const error = new ValidationError('Test error', { field: 'email' });
      const serialized = JSON.parse(JSON.stringify(error));
      
      expect(serialized.message).toBe('Test error');
      expect(serialized.statusCode).toBe(400);
      expect(serialized.code).toBe('VALIDATION_ERROR');
      expect(serialized.details).toEqual({ field: 'email' });
    });
  });
});
