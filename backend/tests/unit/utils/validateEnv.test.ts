import { validateEnv, getEnvConfig, isDevelopment, isProduction, isTest } from '@/utils/validateEnv';

describe('Environment Validation', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // 重置环境变量
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('validateEnv', () => {
    it('should pass validation with all required variables', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key-with-32-chars';

      expect(() => validateEnv()).not.toThrow();
    });

    it('should throw error when DATABASE_URL is missing', () => {
      delete process.env.DATABASE_URL;
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';

      expect(() => validateEnv()).toThrow('Missing required environment variables: DATABASE_URL');
    });

    it('should throw error when REDIS_URL is missing', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      delete process.env.REDIS_URL;
      process.env.JWT_SECRET = 'super-secret-key';

      expect(() => validateEnv()).toThrow('Missing required environment variables: REDIS_URL');
    });

    it('should throw error when JWT_SECRET is missing', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      delete process.env.JWT_SECRET;

      expect(() => validateEnv()).toThrow('Missing required environment variables: JWT_SECRET');
    });

    it('should throw error when multiple required variables are missing', () => {
      delete process.env.DATABASE_URL;
      delete process.env.REDIS_URL;
      delete process.env.JWT_SECRET;

      expect(() => validateEnv()).toThrow('Missing required environment variables: DATABASE_URL, REDIS_URL, JWT_SECRET');
    });

    it('should set default values for optional variables', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';

      // 删除可选变量
      delete process.env.NODE_ENV;
      delete process.env.PORT;
      delete process.env.MCP_CHROME_HOST;

      validateEnv();

      expect(process.env.NODE_ENV).toBe('development');
      expect(process.env.PORT).toBe('8000');
      expect(process.env.MCP_CHROME_HOST).toBe('127.0.0.1');
    });

    it('should validate PORT format', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';
      process.env.PORT = 'invalid-port';

      expect(() => validateEnv()).toThrow('PORT must be a valid port number (1-65535)');
    });

    it('should validate MCP_CHROME_PORT format', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';
      process.env.MCP_CHROME_PORT = '70000'; // 超出范围

      expect(() => validateEnv()).toThrow('MCP_CHROME_PORT must be a valid port number (1-65535)');
    });

    it('should validate URL formats', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';
      process.env.COZE_BASE_URL = 'invalid-url';

      expect(() => validateEnv()).toThrow('COZE_BASE_URL must be a valid URL');
    });

    it('should validate numeric environment variables', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';
      process.env.RATE_LIMIT_WINDOW_MS = 'not-a-number';

      expect(() => validateEnv()).toThrow('RATE_LIMIT_WINDOW_MS must be a positive number');
    });

    it('should warn about short JWT_SECRET', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'short'; // 少于32个字符

      validateEnv();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('JWT_SECRET should be at least 32 characters long')
      );

      consoleSpy.mockRestore();
    });

    it('should warn about non-postgresql DATABASE_URL', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      process.env.DATABASE_URL = 'mysql://user:pass@localhost:3306/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';

      validateEnv();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('DATABASE_URL should start with postgresql://')
      );

      consoleSpy.mockRestore();
    });

    it('should warn about non-redis REDIS_URL', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'memcached://localhost:11211';
      process.env.JWT_SECRET = 'super-secret-key';

      validateEnv();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('REDIS_URL should start with redis://')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('getEnvConfig', () => {
    it('should return environment configuration', () => {
      process.env.NODE_ENV = 'test';
      process.env.PORT = '3000';
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';
      process.env.MCP_CHROME_HOST = '127.0.0.1';
      process.env.MCP_CHROME_PORT = '12306';
      process.env.COZE_BASE_URL = 'https://space.coze.cn';
      process.env.FRONTEND_URL = 'http://localhost:3000';
      process.env.CORS_ORIGIN = 'http://localhost:3000';

      const config = getEnvConfig();

      expect(config).toEqual({
        NODE_ENV: 'test',
        PORT: '3000',
        DATABASE_URL: 'postgresql://user:pass@localhost:5432/db',
        REDIS_URL: 'redis://localhost:6379',
        JWT_SECRET: 'super-secret-key',
        MCP_CHROME_HOST: '127.0.0.1',
        MCP_CHROME_PORT: '12306',
        COZE_BASE_URL: 'https://space.coze.cn',
        FRONTEND_URL: 'http://localhost:3000',
        CORS_ORIGIN: 'http://localhost:3000',
      });
    });
  });

  describe('Environment detection functions', () => {
    it('should detect development environment', () => {
      process.env.NODE_ENV = 'development';
      expect(isDevelopment()).toBe(true);
      expect(isProduction()).toBe(false);
      expect(isTest()).toBe(false);
    });

    it('should detect production environment', () => {
      process.env.NODE_ENV = 'production';
      expect(isDevelopment()).toBe(false);
      expect(isProduction()).toBe(true);
      expect(isTest()).toBe(false);
    });

    it('should detect test environment', () => {
      process.env.NODE_ENV = 'test';
      expect(isDevelopment()).toBe(false);
      expect(isProduction()).toBe(false);
      expect(isTest()).toBe(true);
    });

    it('should handle undefined NODE_ENV', () => {
      delete process.env.NODE_ENV;
      expect(isDevelopment()).toBe(false);
      expect(isProduction()).toBe(false);
      expect(isTest()).toBe(false);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty string values', () => {
      process.env.DATABASE_URL = '';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';

      expect(() => validateEnv()).toThrow('Missing required environment variables: DATABASE_URL');
    });

    it('should handle whitespace-only values', () => {
      process.env.DATABASE_URL = '   ';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';

      expect(() => validateEnv()).toThrow('Missing required environment variables: DATABASE_URL');
    });

    it('should handle port number edge cases', () => {
      process.env.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db';
      process.env.REDIS_URL = 'redis://localhost:6379';
      process.env.JWT_SECRET = 'super-secret-key';
      
      // 测试边界值
      process.env.PORT = '1';
      expect(() => validateEnv()).not.toThrow();

      process.env.PORT = '65535';
      expect(() => validateEnv()).not.toThrow();

      process.env.PORT = '0';
      expect(() => validateEnv()).toThrow();

      process.env.PORT = '65536';
      expect(() => validateEnv()).toThrow();
    });
  });
});
