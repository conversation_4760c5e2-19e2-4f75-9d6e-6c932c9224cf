import request from 'supertest';
import { Express } from 'express';
import { createTestUser, createAuthToken, mockCozeAutomation } from '../setup';

// Mock dependencies
jest.mock('@/mcp/cozeAutomation', () => ({
  CozeAutomation: jest.fn().mockImplementation(() => mockCozeAutomation),
}));

jest.mock('@/index', () => ({
  io: {
    to: jest.fn().mockReturnThis(),
    emit: jest.fn(),
  },
}));

// Import app after mocking
let app: Express;

beforeAll(async () => {
  // 动态导入app以确保mock生效
  const { default: createApp } = await import('@/app');
  app = createApp();
});

describe('API Integration Tests', () => {
  let testUser: any;
  let authToken: string;
  let prisma: any;

  beforeAll(() => {
    prisma = global.__PRISMA__;
  });

  beforeEach(async () => {
    testUser = await createTestUser({
      username: 'apiuser',
      email: '<EMAIL>',
    });
    authToken = createAuthToken(testUser);
  });

  describe('Authentication Flow', () => {
    it('should complete full authentication flow', async () => {
      // 1. 注册新用户
      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'Test123456',
        confirmPassword: 'Test123456',
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(registerData)
        .expect(201);

      expect(registerResponse.body.success).toBe(true);
      expect(registerResponse.body.data.user.email).toBe(registerData.email);
      expect(registerResponse.body.data.token).toBeDefined();

      const { token, refreshToken } = registerResponse.body.data;

      // 2. 使用token获取用户信息
      const meResponse = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(meResponse.body.data.email).toBe(registerData.email);

      // 3. 刷新token
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      expect(refreshResponse.body.data.token).toBeDefined();
      expect(refreshResponse.body.data.refreshToken).toBeDefined();

      // 4. 登出
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      // 5. 验证token已失效（通过检查session）
      const sessions = await prisma.userSession.findMany({
        where: { userId: registerResponse.body.data.user.id },
      });
      expect(sessions).toHaveLength(0);
    });

    it('should handle login flow', async () => {
      const loginData = {
        email: testUser.email,
        password: 'Test123456',
      };

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(loginResponse.body.success).toBe(true);
      expect(loginResponse.body.data.user.id).toBe(testUser.id);
      expect(loginResponse.body.data.token).toBeDefined();
    });
  });

  describe('Generation API Flow', () => {
    it('should complete full generation flow', async () => {
      // 1. 创建生成任务
      const generationData = {
        prompt: 'Write a test article about AI',
        category: 'article',
        options: {
          temperature: 0.7,
          maxTokens: 2000,
        },
      };

      const createResponse = await request(app)
        .post('/api/generation')
        .set('Authorization', `Bearer ${authToken}`)
        .send(generationData)
        .expect(201);

      expect(createResponse.body.success).toBe(true);
      expect(createResponse.body.data.status).toBe('pending');

      const generationId = createResponse.body.data.id;

      // 2. 获取生成任务详情
      const getResponse = await request(app)
        .get(`/api/generation/${generationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(getResponse.body.data.id).toBe(generationId);
      expect(getResponse.body.data.prompt).toBe(generationData.prompt);

      // 3. 等待异步处理完成
      await new Promise(resolve => setTimeout(resolve, 200));

      // 4. 再次获取，验证状态更新
      const updatedResponse = await request(app)
        .get(`/api/generation/${generationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(updatedResponse.body.data.status).toBe('completed');
      expect(updatedResponse.body.data.content).toBeDefined();

      // 5. 获取用户生成历史
      const historyResponse = await request(app)
        .get('/api/generation')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(historyResponse.body.data.generations).toHaveLength(1);
      expect(historyResponse.body.data.generations[0].id).toBe(generationId);

      // 6. 搜索生成记录
      const searchResponse = await request(app)
        .get('/api/generation/search?q=AI')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(searchResponse.body.data.generations).toHaveLength(1);

      // 7. 获取统计信息
      const statsResponse = await request(app)
        .get('/api/generation/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(statsResponse.body.data.total).toBe(1);
      expect(statsResponse.body.data.completed).toBe(1);

      // 8. 删除生成记录
      await request(app)
        .delete(`/api/generation/${generationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // 9. 验证已删除
      await request(app)
        .get(`/api/generation/${generationId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should handle generation validation errors', async () => {
      const invalidData = {
        prompt: '', // 空prompt
        category: 'article',
      };

      const response = await request(app)
        .post('/api/generation')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('VALIDATION_ERROR');
    });

    it('should handle unauthorized access', async () => {
      const generationData = {
        prompt: 'Test prompt',
      };

      // 不提供token
      await request(app)
        .post('/api/generation')
        .send(generationData)
        .expect(401);

      // 提供无效token
      await request(app)
        .post('/api/generation')
        .set('Authorization', 'Bearer invalid-token')
        .send(generationData)
        .expect(401);
    });
  });

  describe('User Profile API', () => {
    it('should handle user profile operations', async () => {
      // 1. 获取用户资料
      const profileResponse = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(profileResponse.body.data.id).toBe(testUser.id);

      // 2. 更新用户资料
      const updateData = {
        avatar: 'https://example.com/new-avatar.jpg',
      };

      const updateResponse = await request(app)
        .put('/api/user/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(updateResponse.body.data.avatar).toBe(updateData.avatar);

      // 3. 获取用户设置
      const settingsResponse = await request(app)
        .get('/api/user/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(settingsResponse.body.data.userId).toBe(testUser.id);

      // 4. 更新用户设置
      const settingsData = {
        theme: 'dark',
        language: 'en-US',
        emailNotifications: false,
      };

      const updateSettingsResponse = await request(app)
        .put('/api/user/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(settingsData)
        .expect(200);

      expect(updateSettingsResponse.body.data.theme).toBe(settingsData.theme);
      expect(updateSettingsResponse.body.data.emailNotifications).toBe(false);
    });
  });

  describe('Health Check API', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('ok');
      expect(response.body.data.services).toBeDefined();
      expect(response.body.data.services.database).toBeDefined();
      expect(response.body.data.services.redis).toBeDefined();
    });

    it('should return individual service health', async () => {
      // 数据库健康检查
      const dbResponse = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(dbResponse.body.data.status).toBe('healthy');

      // Redis健康检查
      const redisResponse = await request(app)
        .get('/api/health/redis')
        .expect(200);

      expect(redisResponse.body.data.status).toBe('healthy');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/api/non-existent-route')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('NOT_FOUND_ERROR');
    });

    it('should handle validation errors consistently', async () => {
      const invalidRegisterData = {
        username: 'ab', // 太短
        email: 'invalid-email',
        password: '123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidRegisterData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('VALIDATION_ERROR');
      expect(response.body.details).toBeDefined();
    });

    it('should handle server errors gracefully', async () => {
      // Mock database error
      const originalCreate = prisma.generation.create;
      prisma.generation.create = jest.fn().mockRejectedValue(new Error('Database error'));

      const generationData = {
        prompt: 'Test prompt',
      };

      const response = await request(app)
        .post('/api/generation')
        .set('Authorization', `Bearer ${authToken}`)
        .send(generationData)
        .expect(500);

      expect(response.body.success).toBe(false);

      // Restore original method
      prisma.generation.create = originalCreate;
    });
  });

  describe('Security', () => {
    it('should enforce rate limiting', async () => {
      // 这个测试需要根据实际的rate limit配置调整
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword',
          })
      );

      const responses = await Promise.all(requests);
      
      // 应该有一些请求被rate limit拦截
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should validate input for XSS attempts', async () => {
      const xssPayload = {
        prompt: '<script>alert("xss")</script>',
      };

      const response = await request(app)
        .post('/api/generation')
        .set('Authorization', `Bearer ${authToken}`)
        .send(xssPayload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('INVALID_INPUT');
    });

    it('should require authentication for protected routes', async () => {
      const protectedRoutes = [
        { method: 'get', path: '/api/generation' },
        { method: 'post', path: '/api/generation' },
        { method: 'get', path: '/api/user/profile' },
        { method: 'get', path: '/api/auth/me' },
      ];

      for (const route of protectedRoutes) {
        const response = await request(app)[route.method](route.path);
        expect(response.status).toBe(401);
      }
    });
  });

  describe('CORS and Headers', () => {
    it('should include security headers', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      // 检查安全头
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
    });

    it('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/api/auth/login')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .expect(204);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toBeDefined();
    });
  });
});
