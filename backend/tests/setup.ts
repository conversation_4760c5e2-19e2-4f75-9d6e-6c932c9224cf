import { PrismaClient } from '@prisma/client';
import Redis from 'ioredis';
import { execSync } from 'child_process';
import dotenv from 'dotenv';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 全局测试变量
declare global {
  var __PRISMA__: PrismaClient;
  var __REDIS__: Redis;
}

// 设置测试数据库
const setupTestDatabase = async () => {
  // 确保测试数据库存在
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    throw new Error('DATABASE_URL is not set for testing');
  }

  // 运行数据库迁移
  try {
    execSync('npx prisma migrate deploy', { stdio: 'inherit' });
    console.log('Database migrations applied successfully');
  } catch (error) {
    console.error('Failed to apply database migrations:', error);
    throw error;
  }

  // 创建Prisma客户端
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: databaseUrl,
      },
    },
  });

  await prisma.$connect();
  global.__PRISMA__ = prisma;

  return prisma;
};

// 设置测试Redis
const setupTestRedis = async () => {
  const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379/1'; // 使用数据库1进行测试
  const redis = new Redis(redisUrl);

  await redis.ping();
  global.__REDIS__ = redis;

  return redis;
};

// 清理测试数据
export const cleanupTestData = async () => {
  const prisma = global.__PRISMA__;
  const redis = global.__REDIS__;

  if (prisma) {
    // 清理数据库表（按依赖关系顺序）
    await prisma.userSession.deleteMany();
    await prisma.generation.deleteMany();
    await prisma.userSettings.deleteMany();
    await prisma.user.deleteMany();
    await prisma.generationTemplate.deleteMany();
    await prisma.systemLog.deleteMany();
    await prisma.apiUsage.deleteMany();
  }

  if (redis) {
    // 清理Redis缓存
    await redis.flushdb();
  }
};

// Jest setup
beforeAll(async () => {
  console.log('Setting up test environment...');
  
  try {
    await setupTestDatabase();
    await setupTestRedis();
    console.log('Test environment setup completed');
  } catch (error) {
    console.error('Failed to setup test environment:', error);
    throw error;
  }
});

beforeEach(async () => {
  // 每个测试前清理数据
  await cleanupTestData();
});

afterAll(async () => {
  console.log('Cleaning up test environment...');
  
  try {
    await cleanupTestData();
    
    if (global.__PRISMA__) {
      await global.__PRISMA__.$disconnect();
    }
    
    if (global.__REDIS__) {
      await global.__REDIS__.quit();
    }
    
    console.log('Test environment cleanup completed');
  } catch (error) {
    console.error('Failed to cleanup test environment:', error);
  }
});

// 测试工具函数
export const createTestUser = async (overrides: any = {}) => {
  const bcrypt = await import('bcryptjs');
  const hashedPassword = await bcrypt.hash('Test123456', 12);
  
  return global.__PRISMA__.user.create({
    data: {
      username: 'testuser',
      email: '<EMAIL>',
      password: hashedPassword,
      ...overrides,
    },
  });
};

export const createTestGeneration = async (userId: string, overrides: any = {}) => {
  return global.__PRISMA__.generation.create({
    data: {
      userId,
      prompt: 'Test prompt',
      content: 'Test content',
      status: 'completed',
      ...overrides,
    },
  });
};

export const createAuthToken = (user: any) => {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    {
      userId: user.id,
      username: user.username,
      email: user.email,
    },
    process.env.JWT_SECRET || 'test-secret',
    { expiresIn: '1h' }
  );
};

// Mock MCP Chrome client
export const mockMCPClient = {
  navigate: jest.fn().mockResolvedValue({ success: true }),
  getWebContent: jest.fn().mockResolvedValue({ 
    success: true, 
    data: 'Mock content' 
  }),
  takeScreenshot: jest.fn().mockResolvedValue({ 
    success: true, 
    data: 'base64-image-data' 
  }),
  clickElement: jest.fn().mockResolvedValue({ success: true }),
  fillForm: jest.fn().mockResolvedValue({ success: true }),
  isConnectedToChrome: jest.fn().mockReturnValue(true),
};

// Mock Coze automation
export const mockCozeAutomation = {
  generateContent: jest.fn().mockResolvedValue({
    content: 'Mock generated content',
    images: [],
    metadata: {
      model: 'mock-model',
      tokens: 100,
      duration: 5000,
    },
  }),
  navigateToCoze: jest.fn().mockResolvedValue(undefined),
  inputUserPrompt: jest.fn().mockResolvedValue(undefined),
  submitGeneration: jest.fn().mockResolvedValue(undefined),
  waitForGenerationResult: jest.fn().mockResolvedValue({
    content: 'Mock generated content',
    images: [],
    metadata: {},
  }),
};

// 设置环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret-key-for-testing';
process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出
