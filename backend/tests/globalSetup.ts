import { execSync } from 'child_process';
import dotenv from 'dotenv';

export default async () => {
  console.log('Global test setup started...');
  
  // 加载测试环境变量
  dotenv.config({ path: '.env.test' });
  
  // 确保测试数据库URL存在
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL must be set for testing');
  }
  
  try {
    // 重置测试数据库
    console.log('Resetting test database...');
    execSync('npx prisma migrate reset --force', { 
      stdio: 'inherit',
      env: { ...process.env, DATABASE_URL: process.env.DATABASE_URL }
    });
    
    // 生成Prisma客户端
    console.log('Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    
    console.log('Global test setup completed');
  } catch (error) {
    console.error('Global test setup failed:', error);
    throw error;
  }
};
