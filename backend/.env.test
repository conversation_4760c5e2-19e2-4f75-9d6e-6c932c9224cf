# 测试环境配置
NODE_ENV=test

# 数据库配置（使用测试数据库）
DATABASE_URL="postgresql://coze_user:coze_password@localhost:5432/coze_clone_test"
REDIS_URL="redis://localhost:6379/1"

# JWT配置
JWT_SECRET="test-super-secret-jwt-key-for-testing-only"
JWT_EXPIRES_IN="1h"

# MCP Chrome配置（测试时使用mock）
MCP_CHROME_HOST="127.0.0.1"
MCP_CHROME_PORT="12306"
MCP_CHROME_TIMEOUT="5000"

# Coze平台配置
COZE_BASE_URL="https://space.coze.cn"
COZE_API_TIMEOUT="10000"

# 服务配置
PORT="8001"
FRONTEND_URL="http://localhost:3001"
CORS_ORIGIN="http://localhost:3001"

# 日志配置
LOG_LEVEL="error"
LOG_FILE_PATH="./test-logs"

# 安全配置
RATE_LIMIT_WINDOW_MS="60000"
RATE_LIMIT_MAX_REQUESTS="1000"

# 缓存配置
CACHE_TTL="60"
CACHE_MAX_SIZE="100"

# 文件上传配置
MAX_FILE_SIZE="1048576"
UPLOAD_PATH="./test-uploads"
