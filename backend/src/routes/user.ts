import { Router } from 'express';
import UserController from '@/controllers/userController';

const router = Router();
const userController = new UserController();

/**
 * @route GET /api/user/profile
 * @desc Get user profile
 * @access Private
 */
router.get(
  '/profile',
  userController.getProfile.bind(userController)
);

/**
 * @route PUT /api/user/profile
 * @desc Update user profile
 * @access Private
 */
router.put(
  '/profile',
  userController.updateProfile.bind(userController)
);

/**
 * @route GET /api/user/settings
 * @desc Get user settings
 * @access Private
 */
router.get(
  '/settings',
  userController.getSettings.bind(userController)
);

/**
 * @route PUT /api/user/settings
 * @desc Update user settings
 * @access Private
 */
router.put(
  '/settings',
  userController.updateSettings.bind(userController)
);

export default router;
