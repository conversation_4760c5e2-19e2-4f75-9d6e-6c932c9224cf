import { Router } from 'express';
import GenerationController, {
  createGenerationValidation,
  getUserGenerationsValidation,
  searchGenerationsValidation,
} from '@/controllers/generationController';

const router = Router();
const generationController = new GenerationController();

/**
 * @route POST /api/generation
 * @desc Create a new generation task
 * @access Private
 */
router.post(
  '/',
  createGenerationValidation,
  generationController.createGeneration.bind(generationController)
);

/**
 * @route GET /api/generation/:id
 * @desc Get generation task details
 * @access Private
 */
router.get(
  '/:id',
  generationController.getGeneration.bind(generationController)
);

/**
 * @route DELETE /api/generation/:id
 * @desc Delete a generation task
 * @access Private
 */
router.delete(
  '/:id',
  generationController.deleteGeneration.bind(generationController)
);

/**
 * @route GET /api/generation
 * @desc Get user's generation history
 * @access Private
 */
router.get(
  '/',
  getUserGenerationsValidation,
  generationController.getUserGenerations.bind(generationController)
);

/**
 * @route GET /api/generation/search
 * @desc Search generation history
 * @access Private
 */
router.get(
  '/search',
  searchGenerationsValidation,
  generationController.searchGenerations.bind(generationController)
);

/**
 * @route GET /api/generation/stats
 * @desc Get generation statistics
 * @access Private
 */
router.get(
  '/stats',
  generationController.getGenerationStats.bind(generationController)
);

export default router;
