import { Router, Request, Response } from 'express';
import { checkDatabaseHealth } from '@/utils/database';
import { checkRedisHealth } from '@/utils/redis';
import { getMCPChromeClient } from '@/mcp/client';

const router = Router();

/**
 * @route GET /api/health
 * @desc Health check endpoint
 * @access Public
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    
    // 检查各个服务的健康状态
    const [dbHealth, redisHealth, mcpHealth] = await Promise.allSettled([
      checkDatabaseHealth(),
      checkRedisHealth(),
      checkMCPHealth(),
    ]);

    const responseTime = Date.now() - startTime;

    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      responseTime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: {
          status: dbHealth.status === 'fulfilled' && dbHealth.value ? 'healthy' : 'unhealthy',
          responseTime: dbHealth.status === 'fulfilled' ? 'ok' : 'error',
        },
        redis: {
          status: redisHealth.status === 'fulfilled' && redisHealth.value ? 'healthy' : 'unhealthy',
          responseTime: redisHealth.status === 'fulfilled' ? 'ok' : 'error',
        },
        mcpChrome: {
          status: mcpHealth.status === 'fulfilled' && mcpHealth.value ? 'healthy' : 'unhealthy',
          responseTime: mcpHealth.status === 'fulfilled' ? 'ok' : 'error',
        },
      },
      memory: {
        used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
        total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
        external: Math.round((process.memoryUsage().external / 1024 / 1024) * 100) / 100,
      },
    };

    // 如果任何关键服务不健康，返回503状态码
    const isHealthy = Object.values(health.services).every(service => service.status === 'healthy');
    const statusCode = isHealthy ? 200 : 503;

    res.status(statusCode).json({
      success: isHealthy,
      data: health,
    });

  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'HEALTH_CHECK_FAILED',
      message: 'Health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @route GET /api/health/database
 * @desc Database health check
 * @access Public
 */
router.get('/database', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    const isHealthy = await checkDatabaseHealth();
    const responseTime = Date.now() - startTime;

    res.json({
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'DATABASE_HEALTH_CHECK_FAILED',
      message: 'Database health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @route GET /api/health/redis
 * @desc Redis health check
 * @access Public
 */
router.get('/redis', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    const isHealthy = await checkRedisHealth();
    const responseTime = Date.now() - startTime;

    res.json({
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'REDIS_HEALTH_CHECK_FAILED',
      message: 'Redis health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

/**
 * @route GET /api/health/mcp
 * @desc MCP Chrome health check
 * @access Public
 */
router.get('/mcp', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    const isHealthy = await checkMCPHealth();
    const responseTime = Date.now() - startTime;

    res.json({
      success: true,
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'MCP_HEALTH_CHECK_FAILED',
      message: 'MCP Chrome health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

// MCP Chrome健康检查函数
async function checkMCPHealth(): Promise<boolean> {
  try {
    const mcpClient = getMCPChromeClient();
    return mcpClient.isConnectedToChrome();
  } catch (error) {
    return false;
  }
}

export default router;
