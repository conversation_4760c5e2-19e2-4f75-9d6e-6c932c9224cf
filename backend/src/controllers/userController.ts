import { Request, Response, NextFunction } from 'express';
import { prisma } from '@/utils/database';
import { logger } from '@/utils/logger';
import { AuthenticationError, NotFoundError } from '@/utils/errors';

export class UserController {
  /**
   * 获取用户资料
   */
  async getProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AuthenticationError('User not authenticated');
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new NotFoundError('User not found');
      }

      res.json({
        success: true,
        data: user,
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户资料
   */
  async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AuthenticationError('User not authenticated');
      }

      const { avatar } = req.body;

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: { avatar },
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info(`User profile updated: ${userId}`);

      res.json({
        success: true,
        data: updatedUser,
        message: 'Profile updated successfully',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户设置
   */
  async getSettings(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AuthenticationError('User not authenticated');
      }

      let settings = await prisma.userSettings.findUnique({
        where: { userId },
      });

      // 如果设置不存在，创建默认设置
      if (!settings) {
        settings = await prisma.userSettings.create({
          data: { userId },
        });
      }

      res.json({
        success: true,
        data: settings,
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户设置
   */
  async updateSettings(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AuthenticationError('User not authenticated');
      }

      const {
        theme,
        language,
        emailNotifications,
        pushNotifications,
        soundNotifications,
        defaultCategory,
        defaultModel,
        defaultTemperature,
        defaultMaxTokens,
        autoSave,
      } = req.body;

      const updateData: any = {};

      if (theme !== undefined) updateData.theme = theme;
      if (language !== undefined) updateData.language = language;
      if (emailNotifications !== undefined) updateData.emailNotifications = emailNotifications;
      if (pushNotifications !== undefined) updateData.pushNotifications = pushNotifications;
      if (soundNotifications !== undefined) updateData.soundNotifications = soundNotifications;
      if (defaultCategory !== undefined) updateData.defaultCategory = defaultCategory;
      if (defaultModel !== undefined) updateData.defaultModel = defaultModel;
      if (defaultTemperature !== undefined) updateData.defaultTemperature = defaultTemperature;
      if (defaultMaxTokens !== undefined) updateData.defaultMaxTokens = defaultMaxTokens;
      if (autoSave !== undefined) updateData.autoSave = autoSave;

      const updatedSettings = await prisma.userSettings.upsert({
        where: { userId },
        update: updateData,
        create: {
          userId,
          ...updateData,
        },
      });

      logger.info(`User settings updated: ${userId}`);

      res.json({
        success: true,
        data: updatedSettings,
        message: 'Settings updated successfully',
      });

    } catch (error) {
      next(error);
    }
  }
}

export default UserController;
