import { Request, Response, NextFunction } from 'express';
import { body, query, validationResult } from 'express-validator';
import GenerationService, { GenerationRequest } from '@/services/generationService';
import { logger } from '@/utils/logger';
import { ValidationError, GenerationError } from '@/utils/errors';

export class GenerationController {
  private generationService: GenerationService;

  constructor() {
    this.generationService = new GenerationService();
  }

  /**
   * 创建新的生成任务
   */
  async createGeneration(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const { prompt, category, options } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        throw new ValidationError('User not authenticated');
      }

      const request: GenerationRequest = {
        prompt,
        category,
        userId,
        options,
      };

      const result = await this.generationService.createGeneration(request);

      res.status(201).json({
        success: true,
        data: result,
        message: 'Generation task created successfully',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取生成任务详情
   */
  async getGeneration(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        throw new ValidationError('User not authenticated');
      }

      const generation = await this.generationService.getGeneration(id, userId);

      if (!generation) {
        res.status(404).json({
          success: false,
          message: 'Generation not found',
        });
        return;
      }

      res.json({
        success: true,
        data: generation,
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户的生成历史
   */
  async getUserGenerations(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const userId = req.user?.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      if (!userId) {
        throw new ValidationError('User not authenticated');
      }

      const result = await this.generationService.getUserGenerations(userId, page, limit);

      res.json({
        success: true,
        data: result,
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除生成任务
   */
  async deleteGeneration(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        throw new ValidationError('User not authenticated');
      }

      const deleted = await this.generationService.deleteGeneration(id, userId);

      if (!deleted) {
        res.status(404).json({
          success: false,
          message: 'Generation not found',
        });
        return;
      }

      res.json({
        success: true,
        message: 'Generation deleted successfully',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 搜索生成历史
   */
  async searchGenerations(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const userId = req.user?.id;
      const query = req.query.q as string;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      if (!userId) {
        throw new ValidationError('User not authenticated');
      }

      if (!query || query.trim().length === 0) {
        throw new ValidationError('Search query is required');
      }

      const result = await this.generationService.searchGenerations(userId, query, page, limit);

      res.json({
        success: true,
        data: result,
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取生成统计信息
   */
  async getGenerationStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;

      if (!userId) {
        throw new ValidationError('User not authenticated');
      }

      const stats = await this.generationService.getGenerationStats(userId);

      res.json({
        success: true,
        data: stats,
      });

    } catch (error) {
      next(error);
    }
  }
}

// 验证中间件
export const createGenerationValidation = [
  body('prompt')
    .notEmpty()
    .withMessage('Prompt is required')
    .isLength({ min: 1, max: 10000 })
    .withMessage('Prompt must be between 1 and 10000 characters'),
  body('category')
    .optional()
    .isString()
    .withMessage('Category must be a string'),
  body('options')
    .optional()
    .isObject()
    .withMessage('Options must be an object'),
  body('options.temperature')
    .optional()
    .isFloat({ min: 0, max: 2 })
    .withMessage('Temperature must be between 0 and 2'),
  body('options.maxTokens')
    .optional()
    .isInt({ min: 1, max: 4000 })
    .withMessage('Max tokens must be between 1 and 4000'),
  body('options.model')
    .optional()
    .isString()
    .withMessage('Model must be a string'),
  body('options.saveToHistory')
    .optional()
    .isBoolean()
    .withMessage('Save to history must be a boolean'),
];

export const getUserGenerationsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

export const searchGenerationsValidation = [
  query('q')
    .notEmpty()
    .withMessage('Search query is required')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Search query must be between 1 and 1000 characters'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

export default GenerationController;
