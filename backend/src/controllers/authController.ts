import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

import { prisma } from '@/utils/database';
import { logger } from '@/utils/logger';
import { ValidationError, AuthenticationError, ConflictError } from '@/utils/errors';

export class AuthController {
  /**
   * 用户注册
   */
  async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const { username, email, password } = req.body;

      // 检查用户是否已存在
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { email },
            { username },
          ],
        },
      });

      if (existingUser) {
        if (existingUser.email === email) {
          throw new ConflictError('Email already registered');
        }
        if (existingUser.username === username) {
          throw new ConflictError('Username already taken');
        }
      }

      // 加密密码
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // 创建用户
      const user = await prisma.user.create({
        data: {
          username,
          email,
          password: hashedPassword,
        },
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      // 创建默认设置
      await prisma.userSettings.create({
        data: {
          userId: user.id,
        },
      });

      // 生成JWT token
      const token = this.generateAccessToken(user);
      const refreshToken = this.generateRefreshToken();

      // 保存refresh token
      await prisma.userSession.create({
        data: {
          userId: user.id,
          refreshToken,
          userAgent: req.get('User-Agent'),
          ipAddress: req.ip,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天
        },
      });

      logger.info(`User registered: ${user.id}`, {
        username: user.username,
        email: user.email,
      });

      res.status(201).json({
        success: true,
        data: {
          user,
          token,
          refreshToken,
        },
        message: 'User registered successfully',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 用户登录
   */
  async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const { email, password } = req.body;

      // 查找用户
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new AuthenticationError('Invalid email or password');
      }

      if (!user.isActive) {
        throw new AuthenticationError('Account is disabled');
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new AuthenticationError('Invalid email or password');
      }

      // 生成JWT token
      const token = this.generateAccessToken(user);
      const refreshToken = this.generateRefreshToken();

      // 保存refresh token
      await prisma.userSession.create({
        data: {
          userId: user.id,
          refreshToken,
          userAgent: req.get('User-Agent'),
          ipAddress: req.ip,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天
        },
      });

      // 清理过期的session
      await this.cleanupExpiredSessions(user.id);

      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      logger.info(`User logged in: ${user.id}`, {
        username: user.username,
        email: user.email,
      });

      res.json({
        success: true,
        data: {
          user: userResponse,
          token,
          refreshToken,
        },
        message: 'Login successful',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 用户登出
   */
  async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        
        try {
          const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string };
          
          // 删除所有用户的session
          await prisma.userSession.deleteMany({
            where: { userId: decoded.userId },
          });

          logger.info(`User logged out: ${decoded.userId}`);
        } catch (error) {
          // Token无效，忽略错误
        }
      }

      res.json({
        success: true,
        message: 'Logout successful',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 刷新token
   */
  async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const { refreshToken } = req.body;

      // 查找session
      const session = await prisma.userSession.findUnique({
        where: { refreshToken },
        include: { user: true },
      });

      if (!session || session.expiresAt < new Date()) {
        throw new AuthenticationError('Invalid or expired refresh token');
      }

      if (!session.user.isActive) {
        throw new AuthenticationError('Account is disabled');
      }

      // 生成新的token
      const newToken = this.generateAccessToken(session.user);
      const newRefreshToken = this.generateRefreshToken();

      // 更新session
      await prisma.userSession.update({
        where: { id: session.id },
        data: {
          refreshToken: newRefreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天
        },
      });

      const userResponse = {
        id: session.user.id,
        username: session.user.username,
        email: session.user.email,
        avatar: session.user.avatar,
        createdAt: session.user.createdAt,
        updatedAt: session.user.updatedAt,
      };

      res.json({
        success: true,
        data: {
          user: userResponse,
          token: newToken,
          refreshToken: newRefreshToken,
        },
        message: 'Token refreshed successfully',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        throw new AuthenticationError('User not authenticated');
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        throw new AuthenticationError('User not found');
      }

      res.json({
        success: true,
        data: user,
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户资料
   */
  async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const userId = req.user?.id;
      if (!userId) {
        throw new AuthenticationError('User not authenticated');
      }

      const { username, email } = req.body;
      const updateData: any = {};

      if (username) {
        // 检查用户名是否已被使用
        const existingUser = await prisma.user.findFirst({
          where: {
            username,
            NOT: { id: userId },
          },
        });

        if (existingUser) {
          throw new ConflictError('Username already taken');
        }

        updateData.username = username;
      }

      if (email) {
        // 检查邮箱是否已被使用
        const existingUser = await prisma.user.findFirst({
          where: {
            email,
            NOT: { id: userId },
          },
        });

        if (existingUser) {
          throw new ConflictError('Email already registered');
        }

        updateData.email = email;
      }

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          username: true,
          email: true,
          avatar: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info(`User profile updated: ${userId}`);

      res.json({
        success: true,
        data: updatedUser,
        message: 'Profile updated successfully',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 修改密码
   */
  async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw new ValidationError('Validation failed', errors.array());
      }

      const userId = req.user?.id;
      if (!userId) {
        throw new AuthenticationError('User not authenticated');
      }

      const { currentPassword, newPassword } = req.body;

      // 获取用户当前密码
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new AuthenticationError('User not found');
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new AuthenticationError('Current password is incorrect');
      }

      // 加密新密码
      const saltRounds = 12;
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      await prisma.user.update({
        where: { id: userId },
        data: { password: hashedNewPassword },
      });

      // 删除所有session，强制重新登录
      await prisma.userSession.deleteMany({
        where: { userId },
      });

      logger.info(`User password changed: ${userId}`);

      res.json({
        success: true,
        message: 'Password changed successfully',
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 生成访问token
   */
  private generateAccessToken(user: any): string {
    return jwt.sign(
      {
        userId: user.id,
        username: user.username,
        email: user.email,
      },
      process.env.JWT_SECRET!,
      {
        expiresIn: process.env.JWT_EXPIRES_IN || '15m',
      }
    );
  }

  /**
   * 生成刷新token
   */
  private generateRefreshToken(): string {
    return uuidv4();
  }

  /**
   * 清理过期的session
   */
  private async cleanupExpiredSessions(userId: string): Promise<void> {
    await prisma.userSession.deleteMany({
      where: {
        userId,
        expiresAt: {
          lt: new Date(),
        },
      },
    });
  }
}

export default AuthController;
