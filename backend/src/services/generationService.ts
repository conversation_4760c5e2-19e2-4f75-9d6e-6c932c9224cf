import { CozeAutomation, CozeGenerationRequest, CozeGenerationResult } from '@/mcp/cozeAutomation';
import { logger } from '@/utils/logger';
import { GenerationError, ValidationError } from '@/utils/errors';
import { redisClient } from '@/utils/redis';
import { prisma } from '@/utils/database';
import { io } from '@/index';

export interface GenerationRequest {
  prompt: string;
  category?: string;
  userId: string;
  options?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    saveToHistory?: boolean;
  };
}

export interface GenerationResponse {
  id: string;
  content: string;
  images?: string[];
  metadata?: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  completedAt?: Date;
  error?: string;
}

export class GenerationService {
  private cozeAutomation: CozeAutomation;
  private readonly CACHE_TTL = parseInt(process.env.CACHE_TTL || '3600'); // 1 hour

  constructor() {
    this.cozeAutomation = new CozeAutomation();
  }

  /**
   * 创建新的生成任务
   */
  async createGeneration(request: GenerationRequest): Promise<GenerationResponse> {
    try {
      // 验证输入
      this.validateGenerationRequest(request);

      // 创建数据库记录
      const generation = await prisma.generation.create({
        data: {
          userId: request.userId,
          prompt: request.prompt,
          category: request.category,
          status: 'pending',
          options: request.options || {},
        },
      });

      logger.info(`Created generation task: ${generation.id}`, {
        userId: request.userId,
        promptLength: request.prompt.length,
      });

      // 异步处理生成任务
      this.processGenerationAsync(generation.id, request);

      return {
        id: generation.id,
        content: '',
        status: 'pending',
        createdAt: generation.createdAt,
      };

    } catch (error) {
      logger.error('Failed to create generation:', error);
      throw new GenerationError(`Failed to create generation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 异步处理生成任务
   */
  private async processGenerationAsync(generationId: string, request: GenerationRequest): Promise<void> {
    try {
      // 更新状态为处理中
      await this.updateGenerationStatus(generationId, 'processing');

      // 发送WebSocket通知
      io.to(`user_${request.userId}`).emit('generation:status', {
        id: generationId,
        status: 'processing',
        message: 'Starting content generation...'
      });

      // 构建Coze请求
      const cozeRequest: CozeGenerationRequest = {
        prompt: request.prompt,
        category: request.category,
        options: request.options,
      };

      // 调用Coze自动化
      const result = await this.cozeAutomation.generateContent(cozeRequest);

      // 保存结果
      await this.saveGenerationResult(generationId, result);

      // 发送完成通知
      io.to(`user_${request.userId}`).emit('generation:completed', {
        id: generationId,
        content: result.content,
        images: result.images,
        metadata: result.metadata,
      });

      logger.info(`Generation completed successfully: ${generationId}`);

    } catch (error) {
      logger.error(`Generation failed: ${generationId}`, error);

      // 更新状态为失败
      await this.updateGenerationStatus(generationId, 'failed', error instanceof Error ? error.message : 'Unknown error');

      // 发送失败通知
      io.to(`user_${request.userId}`).emit('generation:failed', {
        id: generationId,
        error: error instanceof Error ? error.message : 'Generation failed',
      });
    }
  }

  /**
   * 获取生成任务详情
   */
  async getGeneration(id: string, userId: string): Promise<GenerationResponse | null> {
    try {
      // 先从缓存获取
      const cacheKey = `generation:${id}`;
      const cached = await redisClient.get(cacheKey);
      
      if (cached) {
        const generation = JSON.parse(cached);
        if (generation.userId === userId) {
          return generation;
        }
      }

      // 从数据库获取
      const generation = await prisma.generation.findFirst({
        where: {
          id,
          userId,
        },
      });

      if (!generation) {
        return null;
      }

      const response: GenerationResponse = {
        id: generation.id,
        content: generation.content || '',
        images: generation.images as string[] || [],
        metadata: generation.metadata || {},
        status: generation.status as any,
        createdAt: generation.createdAt,
        completedAt: generation.completedAt || undefined,
        error: generation.error || undefined,
      };

      // 缓存结果
      if (generation.status === 'completed' || generation.status === 'failed') {
        await redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(response));
      }

      return response;

    } catch (error) {
      logger.error('Failed to get generation:', error);
      throw new GenerationError(`Failed to get generation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取用户的生成历史
   */
  async getUserGenerations(userId: string, page: number = 1, limit: number = 20): Promise<{
    generations: GenerationResponse[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const offset = (page - 1) * limit;

      const [generations, total] = await Promise.all([
        prisma.generation.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        prisma.generation.count({
          where: { userId },
        }),
      ]);

      const response = generations.map(gen => ({
        id: gen.id,
        content: gen.content || '',
        images: gen.images as string[] || [],
        metadata: gen.metadata || {},
        status: gen.status as any,
        createdAt: gen.createdAt,
        completedAt: gen.completedAt || undefined,
        error: gen.error || undefined,
      }));

      return {
        generations: response,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };

    } catch (error) {
      logger.error('Failed to get user generations:', error);
      throw new GenerationError(`Failed to get user generations: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 删除生成任务
   */
  async deleteGeneration(id: string, userId: string): Promise<boolean> {
    try {
      const result = await prisma.generation.deleteMany({
        where: {
          id,
          userId,
        },
      });

      if (result.count > 0) {
        // 删除缓存
        await redisClient.del(`generation:${id}`);
        logger.info(`Deleted generation: ${id}`);
        return true;
      }

      return false;

    } catch (error) {
      logger.error('Failed to delete generation:', error);
      throw new GenerationError(`Failed to delete generation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 搜索生成历史
   */
  async searchGenerations(userId: string, query: string, page: number = 1, limit: number = 20): Promise<{
    generations: GenerationResponse[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const offset = (page - 1) * limit;

      const [generations, total] = await Promise.all([
        prisma.generation.findMany({
          where: {
            userId,
            OR: [
              { prompt: { contains: query, mode: 'insensitive' } },
              { content: { contains: query, mode: 'insensitive' } },
            ],
          },
          orderBy: { createdAt: 'desc' },
          skip: offset,
          take: limit,
        }),
        prisma.generation.count({
          where: {
            userId,
            OR: [
              { prompt: { contains: query, mode: 'insensitive' } },
              { content: { contains: query, mode: 'insensitive' } },
            ],
          },
        }),
      ]);

      const response = generations.map(gen => ({
        id: gen.id,
        content: gen.content || '',
        images: gen.images as string[] || [],
        metadata: gen.metadata || {},
        status: gen.status as any,
        createdAt: gen.createdAt,
        completedAt: gen.completedAt || undefined,
        error: gen.error || undefined,
      }));

      return {
        generations: response,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };

    } catch (error) {
      logger.error('Failed to search generations:', error);
      throw new GenerationError(`Failed to search generations: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 获取生成统计信息
   */
  async getGenerationStats(userId: string): Promise<{
    total: number;
    completed: number;
    failed: number;
    pending: number;
    processing: number;
  }> {
    try {
      const stats = await prisma.generation.groupBy({
        by: ['status'],
        where: { userId },
        _count: { status: true },
      });

      const result = {
        total: 0,
        completed: 0,
        failed: 0,
        pending: 0,
        processing: 0,
      };

      stats.forEach(stat => {
        result.total += stat._count.status;
        result[stat.status as keyof typeof result] = stat._count.status;
      });

      return result;

    } catch (error) {
      logger.error('Failed to get generation stats:', error);
      throw new GenerationError(`Failed to get generation stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 验证生成请求
   */
  private validateGenerationRequest(request: GenerationRequest): void {
    if (!request.prompt || request.prompt.trim().length === 0) {
      throw new ValidationError('Prompt is required');
    }

    if (request.prompt.length > 10000) {
      throw new ValidationError('Prompt is too long (max 10000 characters)');
    }

    if (!request.userId) {
      throw new ValidationError('User ID is required');
    }
  }

  /**
   * 更新生成状态
   */
  private async updateGenerationStatus(id: string, status: string, error?: string): Promise<void> {
    await prisma.generation.update({
      where: { id },
      data: {
        status,
        error,
        completedAt: status === 'completed' || status === 'failed' ? new Date() : null,
      },
    });
  }

  /**
   * 保存生成结果
   */
  private async saveGenerationResult(id: string, result: CozeGenerationResult): Promise<void> {
    await prisma.generation.update({
      where: { id },
      data: {
        content: result.content,
        images: result.images || [],
        metadata: result.metadata || {},
        status: 'completed',
        completedAt: new Date(),
      },
    });
  }
}

export default GenerationService;
