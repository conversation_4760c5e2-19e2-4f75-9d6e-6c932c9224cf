import mcpClient from '@/mcp/mcpClient';
import logger from '@/utils/logger';

export interface GenerationOptions {
  category?: string;
  temperature?: number;
  maxTokens?: number;
  model?: string;
}

export interface GenerationResult {
  content: string;
  status: 'completed' | 'failed' | 'processing';
  error?: string;
  metadata?: any;
}

export class CozeService {
  private readonly COZE_URL = 'https://www.coze.cn';
  private isLoggedIn = false;

  async initialize(): Promise<void> {
    try {
      // 确保 MCP 客户端已连接
      if (!mcpClient.isClientConnected()) {
        await mcpClient.connect();
      }

      // 导航到 Coze 首页
      await mcpClient.navigate(this.COZE_URL);
      
      // 等待页面加载
      await this.waitForPageLoad();
      
      logger.info('Coze service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Coze service:', error);
      throw error;
    }
  }

  async login(email?: string, password?: string): Promise<boolean> {
    try {
      // 检查是否已经登录
      const content = await mcpClient.getWebContent();
      if (content.textContent?.includes('工作台') || content.textContent?.includes('Dashboard')) {
        this.isLoggedIn = true;
        return true;
      }

      // 如果提供了登录信息，尝试登录
      if (email && password) {
        await this.performLogin(email, password);
        this.isLoggedIn = true;
        return true;
      }

      // 否则假设用户已经在浏览器中登录
      logger.info('Assuming user is already logged in to Coze');
      this.isLoggedIn = true;
      return true;
    } catch (error) {
      logger.error('Failed to login to Coze:', error);
      return false;
    }
  }

  async generateContent(prompt: string, options: GenerationOptions = {}): Promise<GenerationResult> {
    try {
      if (!this.isLoggedIn) {
        await this.login();
      }

      // 导航到创建页面或工作台
      await this.navigateToWorkspace();

      // 创建新的对话或使用现有的
      await this.createNewConversation();

      // 输入提示词
      await this.inputPrompt(prompt);

      // 配置生成选项
      if (options.temperature !== undefined || options.maxTokens !== undefined) {
        await this.configureOptions(options);
      }

      // 开始生成
      await this.startGeneration();

      // 等待生成完成并获取结果
      const result = await this.waitForGenerationComplete();

      return {
        content: result,
        status: 'completed'
      };
    } catch (error) {
      logger.error('Failed to generate content:', error);
      return {
        content: '',
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async waitForPageLoad(): Promise<void> {
    // 等待页面加载完成
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  private async performLogin(email: string, password: string): Promise<void> {
    try {
      // 查找登录按钮
      const elements = await mcpClient.getInteractiveElements({ textQuery: '登录' });
      if (elements.elements && elements.elements.length > 0) {
        await mcpClient.clickElement(elements.elements[0].selector);
      }

      // 等待登录表单加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 填写邮箱
      await mcpClient.fillForm('input[type="email"]', email);
      
      // 填写密码
      await mcpClient.fillForm('input[type="password"]', password);

      // 点击登录按钮
      const loginButton = await mcpClient.getInteractiveElements({ textQuery: '登录' });
      if (loginButton.elements && loginButton.elements.length > 0) {
        await mcpClient.clickElement(loginButton.elements[0].selector);
      }

      // 等待登录完成
      await new Promise(resolve => setTimeout(resolve, 5000));
    } catch (error) {
      logger.error('Login process failed:', error);
      throw error;
    }
  }

  private async navigateToWorkspace(): Promise<void> {
    try {
      // 查找工作台或创建按钮
      const elements = await mcpClient.getInteractiveElements({ 
        textQuery: '工作台' 
      });
      
      if (elements.elements && elements.elements.length > 0) {
        await mcpClient.clickElement(elements.elements[0].selector);
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    } catch (error) {
      logger.warn('Could not navigate to workspace, continuing with current page');
    }
  }

  private async createNewConversation(): Promise<void> {
    try {
      // 查找新建对话或新建按钮
      const elements = await mcpClient.getInteractiveElements({ 
        textQuery: '新建' 
      });
      
      if (elements.elements && elements.elements.length > 0) {
        await mcpClient.clickElement(elements.elements[0].selector);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      logger.warn('Could not create new conversation, using existing interface');
    }
  }

  private async inputPrompt(prompt: string): Promise<void> {
    try {
      // 查找输入框
      const textareas = await mcpClient.getInteractiveElements({ 
        textQuery: '输入' 
      });
      
      if (textareas.elements && textareas.elements.length > 0) {
        await mcpClient.fillForm(textareas.elements[0].selector, prompt);
      } else {
        // 尝试常见的输入框选择器
        const commonSelectors = [
          'textarea',
          'input[type="text"]',
          '[contenteditable="true"]',
          '.input-area textarea',
          '.chat-input textarea'
        ];
        
        for (const selector of commonSelectors) {
          try {
            await mcpClient.fillForm(selector, prompt);
            break;
          } catch (e) {
            continue;
          }
        }
      }
    } catch (error) {
      logger.error('Failed to input prompt:', error);
      throw error;
    }
  }

  private async configureOptions(options: GenerationOptions): Promise<void> {
    // 这里可以根据 Coze 的界面来配置生成选项
    // 由于不同的模型和界面可能不同，这里先跳过
    logger.info('Configuring generation options:', options);
  }

  private async startGeneration(): Promise<void> {
    try {
      // 查找发送或生成按钮
      const buttons = await mcpClient.getInteractiveElements({ 
        textQuery: '发送' 
      });
      
      if (buttons.elements && buttons.elements.length > 0) {
        await mcpClient.clickElement(buttons.elements[0].selector);
      } else {
        // 尝试常见的发送按钮选择器
        const commonSelectors = [
          'button[type="submit"]',
          '.send-button',
          '.submit-button',
          'button:contains("发送")',
          'button:contains("生成")'
        ];
        
        for (const selector of commonSelectors) {
          try {
            await mcpClient.clickElement(selector);
            break;
          } catch (e) {
            continue;
          }
        }
      }
    } catch (error) {
      logger.error('Failed to start generation:', error);
      throw error;
    }
  }

  private async waitForGenerationComplete(): Promise<string> {
    try {
      // 等待生成完成，最多等待60秒
      const maxWaitTime = 60000;
      const checkInterval = 2000;
      let waitTime = 0;

      while (waitTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitTime += checkInterval;

        // 获取页面内容
        const content = await mcpClient.getWebContent();
        
        // 检查是否有新的回复内容
        if (content.textContent && content.textContent.length > 100) {
          // 尝试提取生成的内容
          const generatedContent = this.extractGeneratedContent(content.textContent);
          if (generatedContent && generatedContent.length > 50) {
            return generatedContent;
          }
        }
      }

      throw new Error('Generation timeout');
    } catch (error) {
      logger.error('Failed to wait for generation complete:', error);
      throw error;
    }
  }

  private extractGeneratedContent(pageContent: string): string {
    // 这里需要根据 Coze 的实际页面结构来提取生成的内容
    // 这是一个简化的实现，实际使用时需要根据页面结构调整
    
    // 移除一些常见的界面文本
    const cleanContent = pageContent
      .replace(/导航|菜单|按钮|登录|注册|设置/g, '')
      .replace(/\s+/g, ' ')
      .trim();
    
    // 如果内容太短，可能不是生成的结果
    if (cleanContent.length < 50) {
      return '';
    }
    
    return cleanContent;
  }
}

export default new CozeService();
