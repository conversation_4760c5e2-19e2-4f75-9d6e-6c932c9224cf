import axios, { AxiosInstance } from 'axios';
import { logger } from '@/utils/logger';
import { MCPError } from '@/utils/errors';

export interface MCPChromeConfig {
  host: string;
  port: number;
  timeout: number;
}

export interface MCPResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface NavigateOptions {
  url: string;
  width?: number;
  height?: number;
  newWindow?: boolean;
  refresh?: boolean;
}

export interface ScreenshotOptions {
  fullPage?: boolean;
  width?: number;
  height?: number;
  selector?: string;
  savePng?: boolean;
  storeBase64?: boolean;
  name?: string;
}

export interface ClickOptions {
  selector?: string;
  coordinates?: { x: number; y: number };
  timeout?: number;
  waitForNavigation?: boolean;
}

export interface FillOptions {
  selector: string;
  value: string;
}

export interface WebContentOptions {
  url?: string;
  textContent?: boolean;
  htmlContent?: boolean;
  selector?: string;
}

export interface NetworkCaptureData {
  requests: Array<{
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: string;
    timestamp: number;
  }>;
  responses: Array<{
    url: string;
    status: number;
    headers: Record<string, string>;
    body?: string;
    timestamp: number;
  }>;
}

class MCPChromeClient {
  private client: AxiosInstance;
  private config: MCPChromeConfig;
  private isConnected: boolean = false;

  constructor(config: MCPChromeConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: `http://${config.host}:${config.port}`,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`MCP Chrome Request: ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
        });
        return config;
      },
      (error) => {
        logger.error('MCP Chrome Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`MCP Chrome Response: ${response.status}`, {
          data: response.data,
        });
        return response;
      },
      (error) => {
        logger.error('MCP Chrome Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  async connect(): Promise<void> {
    try {
      const response = await this.client.get('/health');
      if (response.status === 200) {
        this.isConnected = true;
        logger.info('MCP Chrome client connected successfully');
      }
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect to MCP Chrome:', error);
      throw new MCPError('Failed to connect to MCP Chrome service');
    }
  }

  async disconnect(): Promise<void> {
    this.isConnected = false;
    logger.info('MCP Chrome client disconnected');
  }

  private async makeRequest<T>(endpoint: string, data?: any): Promise<MCPResponse<T>> {
    if (!this.isConnected) {
      throw new MCPError('MCP Chrome client is not connected');
    }

    try {
      const response = await this.client.post(endpoint, data);
      return response.data;
    } catch (error: any) {
      logger.error(`MCP Chrome API error for ${endpoint}:`, error);
      throw new MCPError(`MCP Chrome API error: ${error.message}`);
    }
  }

  // Browser navigation methods
  async navigate(options: NavigateOptions): Promise<MCPResponse> {
    return this.makeRequest('/api/navigate', options);
  }

  async goBack(): Promise<MCPResponse> {
    return this.makeRequest('/api/go-back');
  }

  async goForward(): Promise<MCPResponse> {
    return this.makeRequest('/api/go-forward');
  }

  async refresh(): Promise<MCPResponse> {
    return this.makeRequest('/api/refresh');
  }

  // Content extraction methods
  async getWebContent(options: WebContentOptions = {}): Promise<MCPResponse<string>> {
    return this.makeRequest('/api/get-web-content', options);
  }

  async getInteractiveElements(selector?: string): Promise<MCPResponse<any[]>> {
    return this.makeRequest('/api/get-interactive-elements', { selector });
  }

  // Screenshot methods
  async takeScreenshot(options: ScreenshotOptions = {}): Promise<MCPResponse<string>> {
    return this.makeRequest('/api/screenshot', options);
  }

  // Interaction methods
  async clickElement(options: ClickOptions): Promise<MCPResponse> {
    return this.makeRequest('/api/click-element', options);
  }

  async fillForm(options: FillOptions): Promise<MCPResponse> {
    return this.makeRequest('/api/fill-form', options);
  }

  async sendKeys(keys: string, selector?: string): Promise<MCPResponse> {
    return this.makeRequest('/api/send-keys', { keys, selector });
  }

  // Network monitoring methods
  async startNetworkCapture(): Promise<MCPResponse> {
    return this.makeRequest('/api/network/start-capture');
  }

  async stopNetworkCapture(): Promise<MCPResponse<NetworkCaptureData>> {
    return this.makeRequest('/api/network/stop-capture');
  }

  async makeNetworkRequest(url: string, options: any = {}): Promise<MCPResponse> {
    return this.makeRequest('/api/network/request', { url, ...options });
  }

  // Tab management methods
  async getWindowsAndTabs(): Promise<MCPResponse<any[]>> {
    return this.makeRequest('/api/get-windows-and-tabs');
  }

  async closeTabs(tabIds?: number[]): Promise<MCPResponse> {
    return this.makeRequest('/api/close-tabs', { tabIds });
  }

  // Script injection methods
  async injectScript(script: string, type: 'ISOLATED' | 'MAIN' = 'ISOLATED'): Promise<MCPResponse> {
    return this.makeRequest('/api/inject-script', { jsScript: script, type });
  }

  async sendCommandToScript(eventName: string, payload?: any): Promise<MCPResponse> {
    return this.makeRequest('/api/send-command-to-script', { eventName, payload: JSON.stringify(payload) });
  }

  // Search and analysis methods
  async searchTabsContent(query: string): Promise<MCPResponse<any[]>> {
    return this.makeRequest('/api/search-tabs-content', { query });
  }

  // Console methods
  async getConsoleOutput(): Promise<MCPResponse<any[]>> {
    return this.makeRequest('/api/get-console');
  }

  // Bookmark methods
  async searchBookmarks(query: string): Promise<MCPResponse<any[]>> {
    return this.makeRequest('/api/bookmark-search', { query });
  }

  async addBookmark(url: string, title?: string, parentId?: string): Promise<MCPResponse> {
    return this.makeRequest('/api/bookmark-add', { url, title, parentId });
  }

  async deleteBookmark(bookmarkId: string): Promise<MCPResponse> {
    return this.makeRequest('/api/bookmark-delete', { bookmarkId });
  }

  // History methods
  async searchHistory(text?: string, startTime?: string, endTime?: string, maxResults?: number): Promise<MCPResponse<any[]>> {
    return this.makeRequest('/api/history-search', { text, startTime, endTime, maxResults });
  }

  // Utility methods
  isConnectedToChrome(): boolean {
    return this.isConnected;
  }

  getConfig(): MCPChromeConfig {
    return { ...this.config };
  }
}

// Global MCP Chrome client instance
let mcpChromeClient: MCPChromeClient | null = null;

export async function initMCPChrome(): Promise<MCPChromeClient> {
  const config: MCPChromeConfig = {
    host: process.env.MCP_CHROME_HOST || '127.0.0.1',
    port: parseInt(process.env.MCP_CHROME_PORT || '12306'),
    timeout: parseInt(process.env.MCP_CHROME_TIMEOUT || '30000'),
  };

  mcpChromeClient = new MCPChromeClient(config);
  await mcpChromeClient.connect();
  
  return mcpChromeClient;
}

export function getMCPChromeClient(): MCPChromeClient {
  if (!mcpChromeClient) {
    throw new MCPError('MCP Chrome client is not initialized. Call initMCPChrome() first.');
  }
  return mcpChromeClient;
}

export { MCPChromeClient };
