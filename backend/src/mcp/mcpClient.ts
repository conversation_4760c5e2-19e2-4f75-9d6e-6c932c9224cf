import axios from 'axios';
import logger from '@/utils/logger';

export class MCPClient {
  private baseUrl: string;
  private isConnected = false;

  constructor() {
    this.baseUrl = `http://${process.env['MCP_CHROME_HOST'] || '127.0.0.1'}:${process.env['MCP_CHROME_PORT'] || '12306'}`;
  }

  async connect(): Promise<void> {
    try {
      // 测试连接
      const response = await axios.get(`${this.baseUrl}/mcp/health`, {
        timeout: 5000
      });
      
      if (response.status === 200) {
        this.isConnected = true;
        logger.info('MCP Chrome client connected successfully');
      } else {
        throw new Error(`Health check failed with status: ${response.status}`);
      }
    } catch (error) {
      logger.error('Failed to connect to MCP Chrome:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    this.isConnected = false;
    logger.info('MCP Chrome client disconnected');
  }

  async callTool(name: string, arguments_: Record<string, unknown>): Promise<any> {
    if (!this.isConnected) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await axios.post(`${this.baseUrl}/mcp/tools/${name}`, {
        arguments: arguments_
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      logger.error(`Failed to call tool ${name}:`, error);
      throw error;
    }
  }

  async listTools(): Promise<any> {
    if (!this.isConnected) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await axios.get(`${this.baseUrl}/mcp/tools`, {
        timeout: 10000
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to list tools:', error);
      throw error;
    }
  }

  // 导航到指定URL
  async navigate(url: string): Promise<any> {
    return this.callTool('chrome_navigate_mcp-chrome', { url });
  }

  // 获取页面内容
  async getWebContent(options: any = {}): Promise<any> {
    return this.callTool('chrome_get_web_content_mcp-chrome', options);
  }

  // 点击元素
  async clickElement(selector: string): Promise<any> {
    return this.callTool('chrome_click_element_mcp-chrome', { selector });
  }

  // 填写表单
  async fillForm(selector: string, value: string): Promise<any> {
    return this.callTool('chrome_fill_or_select_mcp-chrome', { selector, value });
  }

  // 获取交互元素
  async getInteractiveElements(options: any = {}): Promise<any> {
    return this.callTool('chrome_get_interactive_elements_mcp-chrome', options);
  }

  // 等待元素
  async waitForElement(selector: string, timeout: number = 5000): Promise<any> {
    return this.callTool('chrome_wait_for_element', { selector, timeout });
  }

  isClientConnected(): boolean {
    return this.isConnected;
  }
}

export default new MCPClient();
