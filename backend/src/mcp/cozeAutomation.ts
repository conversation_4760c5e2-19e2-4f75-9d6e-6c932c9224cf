import { getMCPChromeClient } from './client';
import { logger } from '@/utils/logger';
import { MCPError } from '@/utils/errors';

export interface CozeGenerationRequest {
  prompt: string;
  category?: string;
  options?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
  };
}

export interface CozeGenerationResult {
  content: string;
  images?: string[];
  metadata?: {
    model?: string;
    tokens?: number;
    duration?: number;
    category?: string;
  };
  rawResponse?: any;
}

export class CozeAutomation {
  private mcpClient = getMCPChromeClient();
  private readonly COZE_BASE_URL = process.env.COZE_BASE_URL || 'https://space.coze.cn';
  private readonly COZE_TIMEOUT = parseInt(process.env.COZE_API_TIMEOUT || '60000');

  /**
   * 导航到 Coze 平台
   */
  async navigateToCoze(category?: string): Promise<void> {
    try {
      const url = category 
        ? `${this.COZE_BASE_URL}/?category=${category}`
        : this.COZE_BASE_URL;

      logger.info(`Navigating to Coze platform: ${url}`);
      
      const response = await this.mcpClient.navigate({
        url,
        width: 1920,
        height: 1080
      });

      if (!response.success) {
        throw new MCPError(`Failed to navigate to Coze: ${response.error}`);
      }

      // 等待页面加载完成
      await this.waitForPageLoad();
      
      logger.info('Successfully navigated to Coze platform');
    } catch (error) {
      logger.error('Failed to navigate to Coze:', error);
      throw new MCPError(`Navigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 等待页面加载完成
   */
  private async waitForPageLoad(): Promise<void> {
    try {
      // 等待输入框出现
      await this.waitForElement('textarea, input[type="text"], .input-container', 10000);
      
      // 额外等待一秒确保页面完全加载
      await this.sleep(1000);
    } catch (error) {
      logger.warn('Page load wait timeout, continuing anyway');
    }
  }

  /**
   * 等待元素出现
   */
  private async waitForElement(selector: string, timeout: number = 5000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const elements = await this.mcpClient.getInteractiveElements(selector);
        if (elements.success && elements.data && elements.data.length > 0) {
          return;
        }
      } catch (error) {
        // 忽略错误，继续等待
      }
      
      await this.sleep(500);
    }
    
    throw new MCPError(`Element ${selector} not found within ${timeout}ms`);
  }

  /**
   * 在 Coze 平台输入用户需求
   */
  async inputUserPrompt(prompt: string): Promise<void> {
    try {
      logger.info('Inputting user prompt to Coze platform');
      
      // 尝试多个可能的输入框选择器
      const inputSelectors = [
        'textarea[placeholder*="请输入"]',
        'textarea[placeholder*="输入"]',
        'textarea[placeholder*="需求"]',
        '.input-textarea',
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]'
      ];

      let inputFound = false;
      
      for (const selector of inputSelectors) {
        try {
          const elements = await this.mcpClient.getInteractiveElements(selector);
          if (elements.success && elements.data && elements.data.length > 0) {
            // 点击输入框
            await this.mcpClient.clickElement({ selector });
            await this.sleep(500);
            
            // 清空输入框
            await this.mcpClient.sendKeys('Ctrl+A');
            await this.sleep(200);
            
            // 输入内容
            await this.mcpClient.fillForm({ selector, value: prompt });
            await this.sleep(500);
            
            inputFound = true;
            logger.info(`Successfully input prompt using selector: ${selector}`);
            break;
          }
        } catch (error) {
          // 继续尝试下一个选择器
          continue;
        }
      }

      if (!inputFound) {
        throw new MCPError('Could not find input field on Coze platform');
      }

    } catch (error) {
      logger.error('Failed to input prompt:', error);
      throw new MCPError(`Input failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 提交生成请求
   */
  async submitGeneration(): Promise<void> {
    try {
      logger.info('Submitting generation request');
      
      // 尝试多个可能的提交按钮选择器
      const submitSelectors = [
        'button[type="submit"]',
        'button:contains("生成")',
        'button:contains("提交")',
        'button:contains("发送")',
        '.submit-button',
        '.generate-button',
        'button.primary',
        'button[aria-label*="生成"]',
        'button[aria-label*="提交"]'
      ];

      let submitFound = false;

      for (const selector of submitSelectors) {
        try {
          const elements = await this.mcpClient.getInteractiveElements(selector);
          if (elements.success && elements.data && elements.data.length > 0) {
            await this.mcpClient.clickElement({ selector, waitForNavigation: false });
            submitFound = true;
            logger.info(`Successfully clicked submit button using selector: ${selector}`);
            break;
          }
        } catch (error) {
          continue;
        }
      }

      // 如果没找到按钮，尝试按回车键
      if (!submitFound) {
        logger.info('Submit button not found, trying Enter key');
        await this.mcpClient.sendKeys('Enter');
      }

      // 等待生成开始
      await this.sleep(2000);

    } catch (error) {
      logger.error('Failed to submit generation:', error);
      throw new MCPError(`Submit failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 等待生成完成并获取结果
   */
  async waitForGenerationResult(): Promise<CozeGenerationResult> {
    try {
      logger.info('Waiting for generation result');
      
      const maxWaitTime = this.COZE_TIMEOUT;
      const startTime = Date.now();
      
      // 等待生成完成的指示器消失或结果出现
      while (Date.now() - startTime < maxWaitTime) {
        try {
          // 检查是否有加载指示器
          const loadingElements = await this.mcpClient.getInteractiveElements('.loading, .generating, .spinner');
          const hasLoading = loadingElements.success && loadingElements.data && loadingElements.data.length > 0;
          
          // 检查是否有结果内容
          const resultElements = await this.mcpClient.getInteractiveElements('.result, .output, .response, .content');
          const hasResult = resultElements.success && resultElements.data && resultElements.data.length > 0;
          
          if (!hasLoading && hasResult) {
            // 生成完成，获取结果
            return await this.extractGenerationResult();
          }
          
          await this.sleep(2000);
        } catch (error) {
          logger.warn('Error while waiting for result:', error);
          await this.sleep(2000);
        }
      }
      
      // 超时，尝试获取当前页面内容
      logger.warn('Generation timeout, attempting to extract current content');
      return await this.extractGenerationResult();
      
    } catch (error) {
      logger.error('Failed to wait for generation result:', error);
      throw new MCPError(`Wait failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 提取生成结果
   */
  private async extractGenerationResult(): Promise<CozeGenerationResult> {
    try {
      logger.info('Extracting generation result');
      
      // 获取页面内容
      const contentResponse = await this.mcpClient.getWebContent({
        textContent: true
      });
      
      if (!contentResponse.success || !contentResponse.data) {
        throw new MCPError('Failed to get page content');
      }
      
      const pageContent = contentResponse.data;
      
      // 尝试获取HTML内容以提取更多信息
      const htmlResponse = await this.mcpClient.getWebContent({
        htmlContent: true
      });
      
      const htmlContent = htmlResponse.success ? htmlResponse.data : '';
      
      // 提取图片
      const images = this.extractImages(htmlContent);
      
      // 提取主要内容
      const content = this.extractMainContent(pageContent);
      
      // 获取元数据
      const metadata = await this.extractMetadata(htmlContent);
      
      const result: CozeGenerationResult = {
        content,
        images,
        metadata,
        rawResponse: {
          textContent: pageContent,
          htmlContent: htmlContent
        }
      };
      
      logger.info('Successfully extracted generation result', {
        contentLength: content.length,
        imageCount: images.length
      });
      
      return result;
      
    } catch (error) {
      logger.error('Failed to extract generation result:', error);
      throw new MCPError(`Extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 提取图片URL
   */
  private extractImages(htmlContent: string): string[] {
    const images: string[] = [];
    
    try {
      // 使用正则表达式提取图片URL
      const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
      let match;
      
      while ((match = imgRegex.exec(htmlContent)) !== null) {
        const src = match[1];
        if (src && !src.startsWith('data:') && src.includes('http')) {
          images.push(src);
        }
      }
      
      // 去重
      return [...new Set(images)];
    } catch (error) {
      logger.warn('Failed to extract images:', error);
      return [];
    }
  }

  /**
   * 提取主要内容
   */
  private extractMainContent(textContent: string): string {
    try {
      // 移除导航、页脚等无关内容
      const lines = textContent.split('\n');
      const contentLines = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 0 && 
               !trimmed.includes('导航') &&
               !trimmed.includes('菜单') &&
               !trimmed.includes('登录') &&
               !trimmed.includes('注册') &&
               !trimmed.includes('版权') &&
               !trimmed.includes('©');
      });
      
      return contentLines.join('\n').trim();
    } catch (error) {
      logger.warn('Failed to extract main content:', error);
      return textContent;
    }
  }

  /**
   * 提取元数据
   */
  private async extractMetadata(htmlContent: string): Promise<any> {
    try {
      const metadata: any = {};
      
      // 提取页面标题
      const titleMatch = htmlContent.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (titleMatch) {
        metadata.title = titleMatch[1].trim();
      }
      
      // 提取meta信息
      const metaRegex = /<meta[^>]+name=["']([^"']+)["'][^>]+content=["']([^"']+)["'][^>]*>/gi;
      let metaMatch;
      
      while ((metaMatch = metaRegex.exec(htmlContent)) !== null) {
        const name = metaMatch[1];
        const content = metaMatch[2];
        metadata[name] = content;
      }
      
      // 添加时间戳
      metadata.extractedAt = new Date().toISOString();
      
      return metadata;
    } catch (error) {
      logger.warn('Failed to extract metadata:', error);
      return {};
    }
  }

  /**
   * 完整的生成流程
   */
  async generateContent(request: CozeGenerationRequest): Promise<CozeGenerationResult> {
    try {
      logger.info('Starting Coze content generation', { prompt: request.prompt.substring(0, 100) });
      
      // 1. 导航到Coze平台
      await this.navigateToCoze(request.category);
      
      // 2. 输入用户需求
      await this.inputUserPrompt(request.prompt);
      
      // 3. 提交生成请求
      await this.submitGeneration();
      
      // 4. 等待并获取结果
      const result = await this.waitForGenerationResult();
      
      logger.info('Coze content generation completed successfully');
      return result;
      
    } catch (error) {
      logger.error('Coze content generation failed:', error);
      throw error;
    }
  }

  /**
   * 工具方法：睡眠
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 截图保存当前状态（用于调试）
   */
  async takeDebugScreenshot(name: string = 'debug'): Promise<string | null> {
    try {
      const response = await this.mcpClient.takeScreenshot({
        fullPage: true,
        storeBase64: true,
        name: `${name}_${Date.now()}`
      });
      
      if (response.success && response.data) {
        logger.info(`Debug screenshot taken: ${name}`);
        return response.data;
      }
      
      return null;
    } catch (error) {
      logger.warn('Failed to take debug screenshot:', error);
      return null;
    }
  }
}

export default CozeAutomation;
