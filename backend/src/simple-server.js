const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const { createServer } = require('http');
const { Server } = require('socket.io');
const axios = require('axios');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});
const prisma = new PrismaClient();

// MCP Chrome 配置
const MCP_BASE_URL = `http://${process.env.MCP_CHROME_HOST || '127.0.0.1'}:${process.env.MCP_CHROME_PORT || '12306'}`;

// MCP Chrome 客户端类 (支持 streamable-http 协议)
class MCPClient {
  constructor() {
    this.baseUrl = MCP_BASE_URL;
    this.isConnected = false;
    this.sessionId = null;
  }

  async connect() {
    try {
      // 对于 streamable-http 类型，我们需要初始化一个会话
      const initResponse = await axios.post(`${this.baseUrl}/mcp`, {
        jsonrpc: '2.0',
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {}
          },
          clientInfo: {
            name: 'coze-clone-client',
            version: '1.0.0'
          }
        },
        id: 1
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        }
      });

      if (initResponse.status === 200) {
        // 解析 SSE 响应
        const responseText = initResponse.data;
        let jsonData = null;

        if (typeof responseText === 'string') {
          // 解析 SSE 格式: event: message\ndata: {...}
          const lines = responseText.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                jsonData = JSON.parse(line.substring(6));
                break;
              } catch (e) {
                console.warn('Failed to parse SSE data:', line);
              }
            }
          }
        } else {
          jsonData = responseText;
        }

        if (jsonData && jsonData.result) {
          this.isConnected = true;
          console.log('✅ MCP Chrome client connected successfully');
          console.log('Server capabilities:', jsonData.result.capabilities);
          console.log('Server info:', jsonData.result.serverInfo);
          return true;
        } else {
          throw new Error('Invalid initialization response format');
        }
      } else {
        throw new Error(`HTTP ${initResponse.status}: ${initResponse.statusText}`);
      }
    } catch (error) {
      console.error('❌ Failed to connect to MCP Chrome:', error.message);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      this.isConnected = false;
      return false;
    }
  }

  parseSSEResponse(responseText) {
    if (typeof responseText !== 'string') {
      return responseText;
    }

    const lines = responseText.split('\n');
    for (const line of lines) {
      if (line.startsWith('data: ')) {
        try {
          return JSON.parse(line.substring(6));
        } catch (e) {
          console.warn('Failed to parse SSE data:', line);
        }
      }
    }
    return null;
  }

  async callTool(name, arguments_) {
    if (!this.isConnected) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await axios.post(`${this.baseUrl}/mcp`, {
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: name,
          arguments: arguments_
        },
        id: Date.now()
      }, {
        timeout: 60000, // 增加超时时间，因为浏览器操作可能需要更长时间
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        }
      });

      const jsonData = this.parseSSEResponse(response.data);

      if (jsonData && jsonData.error) {
        throw new Error(`MCP Error: ${jsonData.error.message}`);
      }

      return jsonData ? jsonData.result : null;
    } catch (error) {
      console.error(`Failed to call tool ${name}:`, error.message);
      if (error.response) {
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  }

  async listTools() {
    if (!this.isConnected) {
      throw new Error('MCP client not connected');
    }

    try {
      const response = await axios.post(`${this.baseUrl}/mcp`, {
        jsonrpc: '2.0',
        method: 'tools/list',
        params: {},
        id: Date.now()
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/event-stream'
        }
      });

      const jsonData = this.parseSSEResponse(response.data);

      if (jsonData && jsonData.error) {
        throw new Error(`MCP Error: ${jsonData.error.message}`);
      }

      return jsonData ? jsonData.result : null;
    } catch (error) {
      console.error('Failed to list tools:', error.message);
      if (error.response) {
        console.error('Response data:', error.response.data);
      }
      throw error;
    }
  }

  async navigate(url) {
    return this.callTool('chrome_navigate_mcp-chrome', { url });
  }

  async getWebContent(options = {}) {
    return this.callTool('chrome_get_web_content_mcp-chrome', options);
  }

  async clickElement(selector) {
    return this.callTool('chrome_click_element_mcp-chrome', { selector });
  }

  async fillForm(selector, value) {
    return this.callTool('chrome_fill_or_select_mcp-chrome', { selector, value });
  }

  async getInteractiveElements(options = {}) {
    return this.callTool('chrome_get_interactive_elements_mcp-chrome', options);
  }
}

// Coze 服务类
class CozeService {
  constructor() {
    this.mcpClient = new MCPClient();
    this.COZE_URL = 'https://www.coze.cn';
    this.isInitialized = false;
  }

  async initialize() {
    try {
      const connected = await this.mcpClient.connect();
      if (!connected) {
        throw new Error('Failed to connect to MCP Chrome');
      }

      await this.mcpClient.navigate(this.COZE_URL);
      await this.waitForPageLoad();

      this.isInitialized = true;
      console.log('✅ Coze service initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Coze service:', error);
      return false;
    }
  }

  async generateContent(prompt, options = {}) {
    try {
      if (!this.isInitialized) {
        const initialized = await this.initialize();
        if (!initialized) {
          throw new Error('Failed to initialize Coze service');
        }
      }

      console.log(`🚀 Starting content generation for prompt: "${prompt.substring(0, 50)}..."`);

      // 导航到工作台或对话页面
      await this.navigateToWorkspace();

      // 创建新对话或使用现有对话
      await this.createNewConversation();

      // 输入提示词
      await this.inputPrompt(prompt);

      // 开始生成
      await this.startGeneration();

      // 等待生成完成
      const result = await this.waitForGenerationComplete();

      console.log('✅ Content generation completed successfully');
      return {
        content: result,
        status: 'completed'
      };
    } catch (error) {
      console.error('❌ Content generation failed:', error);
      return {
        content: `生成失败: ${error.message}`,
        status: 'failed',
        error: error.message
      };
    }
  }

  async waitForPageLoad() {
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  async navigateToWorkspace() {
    try {
      const elements = await this.mcpClient.getInteractiveElements({ textQuery: '工作台' });
      if (elements.elements && elements.elements.length > 0) {
        await this.mcpClient.clickElement(elements.elements[0].selector);
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    } catch (error) {
      console.warn('Could not navigate to workspace, continuing...');
    }
  }

  async createNewConversation() {
    try {
      const elements = await this.mcpClient.getInteractiveElements({ textQuery: '新建' });
      if (elements.elements && elements.elements.length > 0) {
        await this.mcpClient.clickElement(elements.elements[0].selector);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.warn('Could not create new conversation, using existing interface');
    }
  }

  async inputPrompt(prompt) {
    try {
      // 尝试多种输入框选择器
      const selectors = [
        'textarea',
        'input[type="text"]',
        '[contenteditable="true"]',
        '.input-area textarea',
        '.chat-input textarea',
        '[placeholder*="输入"]',
        '[placeholder*="请输入"]'
      ];

      let success = false;
      for (const selector of selectors) {
        try {
          await this.mcpClient.fillForm(selector, prompt);
          success = true;
          break;
        } catch (e) {
          continue;
        }
      }

      if (!success) {
        throw new Error('Could not find input field');
      }
    } catch (error) {
      console.error('Failed to input prompt:', error);
      throw error;
    }
  }

  async startGeneration() {
    try {
      // 尝试多种发送按钮
      const buttonTexts = ['发送', '生成', '提交', 'Send', 'Generate'];
      let success = false;

      for (const text of buttonTexts) {
        try {
          const elements = await this.mcpClient.getInteractiveElements({ textQuery: text });
          if (elements.elements && elements.elements.length > 0) {
            await this.mcpClient.clickElement(elements.elements[0].selector);
            success = true;
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!success) {
        // 尝试常见的按钮选择器
        const selectors = [
          'button[type="submit"]',
          '.send-button',
          '.submit-button'
        ];

        for (const selector of selectors) {
          try {
            await this.mcpClient.clickElement(selector);
            success = true;
            break;
          } catch (e) {
            continue;
          }
        }
      }

      if (!success) {
        throw new Error('Could not find send button');
      }
    } catch (error) {
      console.error('Failed to start generation:', error);
      throw error;
    }
  }

  async waitForGenerationComplete() {
    try {
      const maxWaitTime = 60000; // 60秒
      const checkInterval = 3000; // 3秒检查一次
      let waitTime = 0;
      let lastContentLength = 0;

      console.log('⏳ Waiting for generation to complete...');

      while (waitTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitTime += checkInterval;

        const content = await this.mcpClient.getWebContent();
        const currentLength = content.textContent ? content.textContent.length : 0;

        // 如果内容长度稳定且足够长，认为生成完成
        if (currentLength > 200 && currentLength === lastContentLength) {
          const generatedContent = this.extractGeneratedContent(content.textContent);
          if (generatedContent && generatedContent.length > 50) {
            return generatedContent;
          }
        }

        lastContentLength = currentLength;
        console.log(`⏳ Waiting... (${waitTime/1000}s/${maxWaitTime/1000}s)`);
      }

      throw new Error('Generation timeout');
    } catch (error) {
      console.error('Failed to wait for generation:', error);
      throw error;
    }
  }

  extractGeneratedContent(pageContent) {
    if (!pageContent) return '';

    // 移除常见的界面元素
    const cleanContent = pageContent
      .replace(/导航|菜单|按钮|登录|注册|设置|工作台|新建|发送/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // 尝试提取主要内容
    const lines = cleanContent.split('\n').filter(line => line.trim().length > 10);
    const mainContent = lines.slice(-5).join('\n').trim(); // 取最后几行作为生成内容

    return mainContent.length > 50 ? mainContent : cleanContent;
  }
}

// 创建全局实例
const cozeService = new CozeService();

// 中间件
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// 健康检查
app.get('/api/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;

    // 检查 MCP Chrome 连接状态
    let mcpStatus = 'disconnected';
    try {
      const mcpClient = new MCPClient();
      const connected = await mcpClient.connect();
      mcpStatus = connected ? 'connected' : 'disconnected';
    } catch (error) {
      mcpStatus = 'error';
    }

    res.json({
      success: true,
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        services: {
          database: { status: 'healthy' },
          server: { status: 'healthy' },
          mcpChrome: {
            status: mcpStatus,
            url: MCP_BASE_URL
          }
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'HEALTH_CHECK_FAILED',
      message: 'Health check failed'
    });
  }
});

// MCP 连接状态检查
app.get('/api/mcp/status', async (req, res) => {
  try {
    const mcpClient = new MCPClient();
    const connected = await mcpClient.connect();

    if (connected) {
      // 尝试获取可用工具列表
      try {
        const tools = await mcpClient.listTools();
        res.json({
          success: true,
          data: {
            status: 'connected',
            url: MCP_BASE_URL,
            toolsCount: tools.tools ? tools.tools.length : 0,
            availableTools: tools.tools ? tools.tools.slice(0, 5).map(t => t.name) : [],
            timestamp: new Date().toISOString()
          }
        });
      } catch (toolError) {
        console.error('Tools list error:', toolError);
        res.json({
          success: true,
          data: {
            status: 'connected_limited',
            url: MCP_BASE_URL,
            error: 'Tools not accessible: ' + toolError.message,
            timestamp: new Date().toISOString()
          }
        });
      }
    } else {
      res.status(503).json({
        success: false,
        error: 'MCP_DISCONNECTED',
        message: 'MCP Chrome service is not available',
        data: {
          status: 'disconnected',
          url: MCP_BASE_URL
        }
      });
    }
  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'MCP_CONNECTION_ERROR',
      message: error.message,
      data: {
        status: 'error',
        url: MCP_BASE_URL
      }
    });
  }
});

// 初始化 Coze 服务
app.post('/api/coze/initialize', async (req, res) => {
  try {
    const initialized = await cozeService.initialize();

    if (initialized) {
      res.json({
        success: true,
        data: {
          status: 'initialized',
          message: 'Coze service initialized successfully'
        }
      });
    } else {
      res.status(503).json({
        success: false,
        error: 'COZE_INIT_FAILED',
        message: 'Failed to initialize Coze service'
      });
    }
  } catch (error) {
    res.status(503).json({
      success: false,
      error: 'COZE_INIT_ERROR',
      message: error.message
    });
  }
});

// 用户注册
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;
    
    // 简单验证
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Missing required fields'
      });
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { username }
        ]
      }
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'USER_EXISTS',
        message: 'User already exists'
      });
    }

    // 创建用户（加密密码）
    const hashedPassword = await bcrypt.hash(password, 12);
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
      }
    });

    // 创建用户设置
    await prisma.userSettings.create({
      data: {
        userId: user.id,
        theme: 'light',
        language: 'zh-CN',
        emailNotifications: true,
        pushNotifications: true,
        soundNotifications: false,
        defaultCategory: 'article',
        defaultTemperature: 0.7,
        defaultMaxTokens: 2000,
        autoSave: true,
      }
    });

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          createdAt: user.createdAt
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'REGISTRATION_FAILED',
      message: 'Registration failed'
    });
  }
});

// 用户登录
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Missing email or password'
      });
    }

    const user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          createdAt: user.createdAt
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'LOGIN_FAILED',
      message: 'Login failed'
    });
  }
});

// 获取当前用户
app.get('/api/auth/me', async (req, res) => {
  // 简化版：返回第一个用户
  try {
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_USER_FAILED',
      message: 'Failed to get user'
    });
  }
});

// 创建生成任务
app.post('/api/generation', async (req, res) => {
  try {
    const { prompt, category = 'article', options = {} } = req.body;

    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Prompt is required'
      });
    }

    // 获取第一个用户
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    // 创建初始生成记录
    const generation = await prisma.generation.create({
      data: {
        userId: user.id,
        prompt,
        category,
        status: 'processing',
        content: '',
        options: JSON.stringify(options),
      }
    });

    // 立即返回生成记录，然后异步处理
    res.status(201).json({
      success: true,
      data: generation
    });

    // 异步调用 Coze 服务生成内容
    (async () => {
      try {
        console.log(`🚀 Starting real content generation for: "${prompt}"`);

        // 通过 WebSocket 发送开始生成的消息
        io.emit('generation_started', {
          generationId: generation.id,
          prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : '')
        });

        // 调用真实的 Coze 服务
        const result = await cozeService.generateContent(prompt, options);

        // 更新数据库记录
        const updatedGeneration = await prisma.generation.update({
          where: { id: generation.id },
          data: {
            content: result.content,
            status: result.status === 'completed' ? 'completed' : 'failed',
            completedAt: result.status === 'completed' ? new Date() : null,
            error: result.error || null
          }
        });

        // 通过 WebSocket 发送完成消息
        io.emit('generation_completed', {
          generationId: generation.id,
          status: result.status,
          content: result.content,
          error: result.error
        });

        console.log(`✅ Content generation completed for ID: ${generation.id}`);
      } catch (error) {
        console.error('❌ Async generation failed:', error);

        // 更新为失败状态
        await prisma.generation.update({
          where: { id: generation.id },
          data: {
            status: 'failed',
            error: error.message,
            completedAt: new Date()
          }
        });

        // 发送失败消息
        io.emit('generation_failed', {
          generationId: generation.id,
          error: error.message
        });
      }
    })();

  } catch (error) {
    console.error('Generation error:', error);
    res.status(500).json({
      success: false,
      error: 'GENERATION_FAILED',
      message: 'Generation failed'
    });
  }
});

// 获取生成历史
app.get('/api/generation', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [generations, total] = await Promise.all([
      prisma.generation.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.generation.count()
    ]);

    res.json({
      success: true,
      data: {
        generations,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get generations error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_GENERATIONS_FAILED',
      message: 'Failed to get generations'
    });
  }
});

// 获取单个生成记录
app.get('/api/generation/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const generation = await prisma.generation.findUnique({
      where: { id }
    });

    if (!generation) {
      return res.status(404).json({
        success: false,
        error: 'NOT_FOUND',
        message: 'Generation not found'
      });
    }

    res.json({
      success: true,
      data: generation
    });
  } catch (error) {
    console.error('Get generation error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_GENERATION_FAILED',
      message: 'Failed to get generation'
    });
  }
});

// 获取用户设置
app.get('/api/user/settings', async (req, res) => {
  try {
    // 获取第一个用户
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    const settings = await prisma.userSettings.findUnique({
      where: { userId: user.id }
    });

    if (!settings) {
      // 创建默认设置
      const defaultSettings = await prisma.userSettings.create({
        data: {
          userId: user.id,
          theme: 'light',
          language: 'zh-CN',
          emailNotifications: true,
          pushNotifications: true,
          soundNotifications: false,
          defaultCategory: 'article',
          defaultTemperature: 0.7,
          defaultMaxTokens: 2000,
          autoSave: true,
        }
      });

      return res.json({
        success: true,
        data: defaultSettings
      });
    }

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_SETTINGS_FAILED',
      message: 'Failed to get settings'
    });
  }
});

// 更新用户设置
app.put('/api/user/settings', async (req, res) => {
  try {
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    const updatedSettings = await prisma.userSettings.upsert({
      where: { userId: user.id },
      update: req.body,
      create: {
        userId: user.id,
        ...req.body
      }
    });

    res.json({
      success: true,
      data: updatedSettings
    });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      success: false,
      error: 'UPDATE_SETTINGS_FAILED',
      message: 'Failed to update settings'
    });
  }
});

// 获取用户资料
app.get('/api/user/profile', async (req, res) => {
  try {
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_PROFILE_FAILED',
      message: 'Failed to get profile'
    });
  }
});

// 更新用户资料
app.put('/api/user/profile', async (req, res) => {
  try {
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    const { username, email, avatar } = req.body;

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        ...(username && { username }),
        ...(email && { email }),
        ...(avatar && { avatar }),
      }
    });

    res.json({
      success: true,
      data: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        avatar: updatedUser.avatar,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'UPDATE_PROFILE_FAILED',
      message: 'Failed to update profile'
    });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'INTERNAL_SERVER_ERROR',
    message: 'Internal server error'
  });
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'NOT_FOUND',
    message: 'Route not found'
  });
});

// WebSocket 连接处理
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });

  // 发送连接成功消息
  socket.emit('connected', { message: 'WebSocket connected successfully' });
});

const PORT = process.env.PORT || 8000;

server.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔌 WebSocket server ready`);
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await prisma.$disconnect();
  process.exit(0);
});
