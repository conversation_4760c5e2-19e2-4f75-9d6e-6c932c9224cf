const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');

const app = express();
const prisma = new PrismaClient();

// 中间件
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// 健康检查
app.get('/api/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      success: true,
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        services: {
          database: { status: 'healthy' },
          server: { status: 'healthy' }
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'HEALTH_CHECK_FAILED',
      message: 'Health check failed'
    });
  }
});

// 用户注册
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;
    
    // 简单验证
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Missing required fields'
      });
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email },
          { username }
        ]
      }
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        error: 'USER_EXISTS',
        message: 'User already exists'
      });
    }

    // 创建用户（加密密码）
    const hashedPassword = await bcrypt.hash(password, 12);
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
      }
    });

    // 创建用户设置
    await prisma.userSettings.create({
      data: {
        userId: user.id,
        theme: 'light',
        language: 'zh-CN',
        emailNotifications: true,
        pushNotifications: true,
        soundNotifications: false,
        defaultCategory: 'article',
        defaultTemperature: 0.7,
        defaultMaxTokens: 2000,
        autoSave: true,
      }
    });

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          createdAt: user.createdAt
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      error: 'REGISTRATION_FAILED',
      message: 'Registration failed'
    });
  }
});

// 用户登录
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Missing email or password'
      });
    }

    const user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          createdAt: user.createdAt
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token'
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'LOGIN_FAILED',
      message: 'Login failed'
    });
  }
});

// 获取当前用户
app.get('/api/auth/me', async (req, res) => {
  // 简化版：返回第一个用户
  try {
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_USER_FAILED',
      message: 'Failed to get user'
    });
  }
});

// 创建生成任务
app.post('/api/generation', async (req, res) => {
  try {
    const { prompt, category = 'article', options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Prompt is required'
      });
    }

    // 获取第一个用户
    const user = await prisma.user.findFirst();
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User not found'
      });
    }

    const generation = await prisma.generation.create({
      data: {
        userId: user.id,
        prompt,
        category,
        status: 'completed', // 直接设为完成
        content: `这是对提示词 "${prompt}" 的模拟生成结果。\n\n在实际应用中，这里会调用 MCP Chrome 来访问 Coze 平台并获取真实的生成结果。`,
        options: JSON.stringify(options),
        completedAt: new Date(),
      }
    });

    res.status(201).json({
      success: true,
      data: generation
    });
  } catch (error) {
    console.error('Generation error:', error);
    res.status(500).json({
      success: false,
      error: 'GENERATION_FAILED',
      message: 'Generation failed'
    });
  }
});

// 获取生成历史
app.get('/api/generation', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [generations, total] = await Promise.all([
      prisma.generation.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.generation.count()
    ]);

    res.json({
      success: true,
      data: {
        generations,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get generations error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_GENERATIONS_FAILED',
      message: 'Failed to get generations'
    });
  }
});

// 获取单个生成记录
app.get('/api/generation/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const generation = await prisma.generation.findUnique({
      where: { id }
    });

    if (!generation) {
      return res.status(404).json({
        success: false,
        error: 'NOT_FOUND',
        message: 'Generation not found'
      });
    }

    res.json({
      success: true,
      data: generation
    });
  } catch (error) {
    console.error('Get generation error:', error);
    res.status(500).json({
      success: false,
      error: 'GET_GENERATION_FAILED',
      message: 'Failed to get generation'
    });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'INTERNAL_SERVER_ERROR',
    message: 'Internal server error'
  });
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'NOT_FOUND',
    message: 'Route not found'
  });
});

const PORT = process.env.PORT || 8000;

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  await prisma.$disconnect();
  process.exit(0);
});
