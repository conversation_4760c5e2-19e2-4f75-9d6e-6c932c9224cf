import winston from 'winston';
import path from 'path';

// 定义日志级别
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// 定义日志颜色
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// 添加颜色到winston
winston.addColors(colors);

// 定义日志格式
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${
      info.stack ? '\n' + info.stack : ''
    }${
      info.details ? '\n' + JSON.stringify(info.details, null, 2) : ''
    }`
  )
);

// 定义传输器
const transports = [
  // 控制台输出
  new winston.transports.Console({
    format: format,
  }),
];

// 在生产环境中添加文件输出
if (process.env.NODE_ENV === 'production') {
  const logDir = process.env.LOG_FILE_PATH || './logs';
  
  transports.push(
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    }),
    // 所有日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    })
  );
}

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports,
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
  ],
  // 处理未处理的Promise拒绝
  rejectionHandlers: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
  ],
});

// 在非生产环境中，不退出进程
if (process.env.NODE_ENV !== 'production') {
  logger.exitOnError = false;
}

// 扩展logger功能
interface ExtendedLogger extends winston.Logger {
  request: (message: string, details?: any) => void;
  response: (message: string, details?: any) => void;
  database: (message: string, details?: any) => void;
  mcp: (message: string, details?: any) => void;
  generation: (message: string, details?: any) => void;
}

const extendedLogger = logger as ExtendedLogger;

// 添加自定义日志方法
extendedLogger.request = (message: string, details?: any) => {
  logger.http(`[REQUEST] ${message}`, details);
};

extendedLogger.response = (message: string, details?: any) => {
  logger.http(`[RESPONSE] ${message}`, details);
};

extendedLogger.database = (message: string, details?: any) => {
  logger.debug(`[DATABASE] ${message}`, details);
};

extendedLogger.mcp = (message: string, details?: any) => {
  logger.info(`[MCP] ${message}`, details);
};

extendedLogger.generation = (message: string, details?: any) => {
  logger.info(`[GENERATION] ${message}`, details);
};

export { extendedLogger as logger };
export default extendedLogger;
