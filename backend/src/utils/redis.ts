import Redis from 'ioredis';
import { logger } from './logger';
import { DatabaseError } from './errors';

// Redis客户端实例
export let redisClient: Redis;

/**
 * 连接到Redis
 */
export async function connectRedis(): Promise<void> {
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    
    redisClient = new Redis(redisUrl, {
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      // 连接超时
      connectTimeout: 10000,
      // 命令超时
      commandTimeout: 5000,
      // 重试配置
      retryDelayOnClusterDown: 300,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    });

    // 设置事件监听器
    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      logger.info('Redis client ready');
    });

    redisClient.on('error', (error) => {
      logger.error('Redis client error:', error);
    });

    redisClient.on('close', () => {
      logger.warn('Redis client connection closed');
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis client reconnecting...');
    });

    redisClient.on('end', () => {
      logger.warn('Redis client connection ended');
    });

    // 连接到Redis
    await redisClient.connect();
    
    // 测试连接
    await redisClient.ping();
    logger.info('Redis connection test passed');

  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw new DatabaseError('Redis connection failed');
  }
}

/**
 * 断开Redis连接
 */
export async function disconnectRedis(): Promise<void> {
  try {
    if (redisClient) {
      await redisClient.quit();
      logger.info('Redis disconnected successfully');
    }
  } catch (error) {
    logger.error('Failed to disconnect from Redis:', error);
  }
}

/**
 * 检查Redis连接状态
 */
export async function checkRedisHealth(): Promise<boolean> {
  try {
    if (!redisClient) {
      return false;
    }
    
    const result = await redisClient.ping();
    return result === 'PONG';
  } catch (error) {
    logger.error('Redis health check failed:', error);
    return false;
  }
}

/**
 * 缓存辅助类
 */
export class CacheService {
  private defaultTTL: number;

  constructor(defaultTTL: number = 3600) {
    this.defaultTTL = defaultTTL;
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      const expiration = ttl || this.defaultTTL;
      
      await redisClient.setex(key, expiration, serializedValue);
      logger.debug(`Cache set: ${key}`, { ttl: expiration });
    } catch (error) {
      logger.error(`Failed to set cache for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await redisClient.get(key);
      
      if (value === null) {
        logger.debug(`Cache miss: ${key}`);
        return null;
      }
      
      logger.debug(`Cache hit: ${key}`);
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error(`Failed to get cache for key ${key}:`, error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      await redisClient.del(key);
      logger.debug(`Cache deleted: ${key}`);
    } catch (error) {
      logger.error(`Failed to delete cache for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error) {
      logger.error(`Failed to check cache existence for key ${key}:`, error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      await redisClient.expire(key, ttl);
      logger.debug(`Cache expiration set: ${key}`, { ttl });
    } catch (error) {
      logger.error(`Failed to set expiration for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      return await redisClient.ttl(key);
    } catch (error) {
      logger.error(`Failed to get TTL for key ${key}:`, error);
      return -1;
    }
  }

  /**
   * 批量设置缓存
   */
  async mset(keyValuePairs: Record<string, any>, ttl?: number): Promise<void> {
    try {
      const pipeline = redisClient.pipeline();
      const expiration = ttl || this.defaultTTL;
      
      for (const [key, value] of Object.entries(keyValuePairs)) {
        const serializedValue = JSON.stringify(value);
        pipeline.setex(key, expiration, serializedValue);
      }
      
      await pipeline.exec();
      logger.debug(`Batch cache set: ${Object.keys(keyValuePairs).length} keys`);
    } catch (error) {
      logger.error('Failed to batch set cache:', error);
      throw error;
    }
  }

  /**
   * 批量获取缓存
   */
  async mget<T>(keys: string[]): Promise<Record<string, T | null>> {
    try {
      const values = await redisClient.mget(...keys);
      const result: Record<string, T | null> = {};
      
      keys.forEach((key, index) => {
        const value = values[index];
        result[key] = value ? JSON.parse(value) as T : null;
      });
      
      logger.debug(`Batch cache get: ${keys.length} keys`);
      return result;
    } catch (error) {
      logger.error('Failed to batch get cache:', error);
      throw error;
    }
  }

  /**
   * 模糊匹配删除缓存
   */
  async delPattern(pattern: string): Promise<number> {
    try {
      const keys = await redisClient.keys(pattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      const result = await redisClient.del(...keys);
      logger.debug(`Pattern cache deleted: ${pattern}`, { count: result });
      return result;
    } catch (error) {
      logger.error(`Failed to delete cache pattern ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * 增加计数器
   */
  async incr(key: string, ttl?: number): Promise<number> {
    try {
      const result = await redisClient.incr(key);
      
      if (ttl && result === 1) {
        await redisClient.expire(key, ttl);
      }
      
      return result;
    } catch (error) {
      logger.error(`Failed to increment counter for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * 减少计数器
   */
  async decr(key: string): Promise<number> {
    try {
      return await redisClient.decr(key);
    } catch (error) {
      logger.error(`Failed to decrement counter for key ${key}:`, error);
      throw error;
    }
  }
}

// 创建默认缓存服务实例
export const cacheService = new CacheService();

// 优雅关闭处理
process.on('beforeExit', async () => {
  await disconnectRedis();
});

process.on('SIGINT', async () => {
  await disconnectRedis();
});

process.on('SIGTERM', async () => {
  await disconnectRedis();
});

export default redisClient;
