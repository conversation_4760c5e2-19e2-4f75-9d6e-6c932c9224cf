import { PrismaClient } from '@prisma/client';
import { logger } from './logger';
import { DatabaseError } from './errors';

// 创建Prisma客户端实例
export const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
});

// 设置日志事件监听器
prisma.$on('query', (e) => {
  logger.database(`Query: ${e.query}`, {
    params: e.params,
    duration: `${e.duration}ms`,
  });
});

prisma.$on('error', (e) => {
  logger.error(`Database error: ${e.message}`, {
    target: e.target,
  });
});

prisma.$on('info', (e) => {
  logger.database(`Database info: ${e.message}`, {
    target: e.target,
  });
});

prisma.$on('warn', (e) => {
  logger.warn(`Database warning: ${e.message}`, {
    target: e.target,
  });
});

/**
 * 连接到数据库
 */
export async function connectDatabase(): Promise<void> {
  try {
    await prisma.$connect();
    logger.info('Database connected successfully');
    
    // 测试数据库连接
    await prisma.$queryRaw`SELECT 1`;
    logger.info('Database connection test passed');
    
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw new DatabaseError('Database connection failed');
  }
}

/**
 * 断开数据库连接
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('Database disconnected successfully');
  } catch (error) {
    logger.error('Failed to disconnect from database:', error);
  }
}

/**
 * 检查数据库连接状态
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
}

/**
 * 执行数据库迁移
 */
export async function runMigrations(): Promise<void> {
  try {
    // 在生产环境中，迁移应该通过CI/CD管道执行
    if (process.env.NODE_ENV === 'production') {
      logger.warn('Skipping migrations in production environment');
      return;
    }
    
    // 在开发环境中可以自动执行迁移
    logger.info('Running database migrations...');
    // 这里可以添加迁移逻辑
    logger.info('Database migrations completed');
    
  } catch (error) {
    logger.error('Failed to run migrations:', error);
    throw new DatabaseError('Migration failed');
  }
}

/**
 * 数据库事务辅助函数
 */
export async function withTransaction<T>(
  callback: (tx: PrismaClient) => Promise<T>
): Promise<T> {
  try {
    return await prisma.$transaction(async (tx) => {
      return await callback(tx);
    });
  } catch (error) {
    logger.error('Transaction failed:', error);
    throw new DatabaseError('Transaction failed');
  }
}

/**
 * 批量操作辅助函数
 */
export async function batchOperation<T>(
  operations: Array<() => Promise<T>>,
  batchSize: number = 10
): Promise<T[]> {
  const results: T[] = [];
  
  for (let i = 0; i < operations.length; i += batchSize) {
    const batch = operations.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(op => op()));
    results.push(...batchResults);
    
    logger.database(`Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(operations.length / batchSize)}`);
  }
  
  return results;
}

/**
 * 软删除辅助函数
 */
export async function softDelete(model: any, where: any): Promise<any> {
  return await model.update({
    where,
    data: {
      deletedAt: new Date(),
    },
  });
}

/**
 * 查找未删除的记录
 */
export async function findManyNotDeleted(model: any, args?: any): Promise<any> {
  return await model.findMany({
    ...args,
    where: {
      ...args?.where,
      deletedAt: null,
    },
  });
}

// 优雅关闭处理
process.on('beforeExit', async () => {
  await disconnectDatabase();
});

process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
  process.exit(0);
});

export default prisma;
