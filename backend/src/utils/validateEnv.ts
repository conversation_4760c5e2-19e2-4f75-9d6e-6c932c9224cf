import { logger } from './logger';

interface EnvConfig {
  NODE_ENV: string;
  PORT: string;
  DATABASE_URL: string;
  REDIS_URL: string;
  JWT_SECRET: string;
  MCP_CHROME_HOST: string;
  MCP_CHROME_PORT: string;
  COZE_BASE_URL: string;
  FRONTEND_URL: string;
  CORS_ORIGIN: string;
}

const requiredEnvVars: (keyof EnvConfig)[] = [
  'DATABASE_URL',
  'REDIS_URL',
  'JWT_SECRET',
];

const defaultValues: Partial<EnvConfig> = {
  NODE_ENV: 'development',
  PORT: '8000',
  MCP_CHROME_HOST: '127.0.0.1',
  MCP_CHROME_PORT: '12306',
  COZE_BASE_URL: 'https://space.coze.cn',
  FRONTEND_URL: 'http://localhost:3000',
  CORS_ORIGIN: 'http://localhost:3000',
};

export function validateEnv(): void {
  const missingVars: string[] = [];
  const warnings: string[] = [];

  // 检查必需的环境变量
  for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  }

  // 如果有缺失的必需变量，抛出错误
  if (missingVars.length > 0) {
    logger.error('Missing required environment variables:', missingVars);
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // 设置默认值并检查可选变量
  for (const [key, defaultValue] of Object.entries(defaultValues)) {
    if (!process.env[key]) {
      process.env[key] = defaultValue;
      warnings.push(`${key} not set, using default: ${defaultValue}`);
    }
  }

  // 验证特定格式
  validateSpecificFormats();

  // 输出警告
  if (warnings.length > 0) {
    logger.warn('Environment variable warnings:', warnings);
  }

  logger.info('Environment validation completed successfully');
}

function validateSpecificFormats(): void {
  // 验证端口号
  const port = parseInt(process.env.PORT || '8000');
  if (isNaN(port) || port < 1 || port > 65535) {
    throw new Error('PORT must be a valid port number (1-65535)');
  }

  // 验证MCP Chrome端口
  const mcpPort = parseInt(process.env.MCP_CHROME_PORT || '12306');
  if (isNaN(mcpPort) || mcpPort < 1 || mcpPort > 65535) {
    throw new Error('MCP_CHROME_PORT must be a valid port number (1-65535)');
  }

  // 验证数据库URL格式
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl && !dbUrl.startsWith('postgresql://')) {
    logger.warn('DATABASE_URL should start with postgresql://');
  }

  // 验证Redis URL格式
  const redisUrl = process.env.REDIS_URL;
  if (redisUrl && !redisUrl.startsWith('redis://')) {
    logger.warn('REDIS_URL should start with redis://');
  }

  // 验证JWT密钥长度
  const jwtSecret = process.env.JWT_SECRET;
  if (jwtSecret && jwtSecret.length < 32) {
    logger.warn('JWT_SECRET should be at least 32 characters long for security');
  }

  // 验证URL格式
  const urls = [
    { name: 'COZE_BASE_URL', value: process.env.COZE_BASE_URL },
    { name: 'FRONTEND_URL', value: process.env.FRONTEND_URL },
    { name: 'CORS_ORIGIN', value: process.env.CORS_ORIGIN },
  ];

  for (const { name, value } of urls) {
    if (value && !isValidUrl(value)) {
      throw new Error(`${name} must be a valid URL`);
    }
  }

  // 验证数值类型的环境变量
  const numericVars = [
    { name: 'RATE_LIMIT_WINDOW_MS', defaultValue: '900000' },
    { name: 'RATE_LIMIT_MAX_REQUESTS', defaultValue: '100' },
    { name: 'MCP_CHROME_TIMEOUT', defaultValue: '30000' },
    { name: 'COZE_API_TIMEOUT', defaultValue: '60000' },
    { name: 'CACHE_TTL', defaultValue: '3600' },
    { name: 'CACHE_MAX_SIZE', defaultValue: '1000' },
    { name: 'MAX_FILE_SIZE', defaultValue: '10485760' },
  ];

  for (const { name, defaultValue } of numericVars) {
    const value = process.env[name] || defaultValue;
    const numValue = parseInt(value);
    
    if (isNaN(numValue) || numValue < 0) {
      throw new Error(`${name} must be a positive number`);
    }
    
    if (!process.env[name]) {
      process.env[name] = defaultValue;
    }
  }
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// 获取环境配置的辅助函数
export function getEnvConfig(): EnvConfig {
  return {
    NODE_ENV: process.env.NODE_ENV!,
    PORT: process.env.PORT!,
    DATABASE_URL: process.env.DATABASE_URL!,
    REDIS_URL: process.env.REDIS_URL!,
    JWT_SECRET: process.env.JWT_SECRET!,
    MCP_CHROME_HOST: process.env.MCP_CHROME_HOST!,
    MCP_CHROME_PORT: process.env.MCP_CHROME_PORT!,
    COZE_BASE_URL: process.env.COZE_BASE_URL!,
    FRONTEND_URL: process.env.FRONTEND_URL!,
    CORS_ORIGIN: process.env.CORS_ORIGIN!,
  };
}

// 检查是否为开发环境
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

// 检查是否为生产环境
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

// 检查是否为测试环境
export function isTest(): boolean {
  return process.env.NODE_ENV === 'test';
}
