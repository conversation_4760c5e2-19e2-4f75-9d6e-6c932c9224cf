import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { RateLimitError } from '@/utils/errors';
import { logger } from '@/utils/logger';
import { redisClient } from '@/utils/redis';

// 通用限流中间件
export const createRateLimit = (options: {
  windowMs: number;
  max: number;
  message?: string;
  keyGenerator?: (req: Request) => string;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: {
      success: false,
      error: 'RATE_LIMIT_EXCEEDED',
      message: options.message || 'Too many requests, please try again later.',
    },
    keyGenerator: options.keyGenerator || ((req) => req.ip),
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url,
        method: req.method,
      });
      
      res.status(429).json({
        success: false,
        error: 'RATE_LIMIT_EXCEEDED',
        message: options.message || 'Too many requests, please try again later.',
      });
    },
  });
};

// 登录限流
export const loginRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: 'Too many login attempts, please try again in 15 minutes.',
  keyGenerator: (req) => `login:${req.ip}:${req.body.email}`,
});

// 注册限流
export const registerRateLimit = createRateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 最多3次注册
  message: 'Too many registration attempts, please try again in 1 hour.',
});

// 生成内容限流
export const generationRateLimit = createRateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 最多10次生成
  message: 'Too many generation requests, please try again in 1 minute.',
  keyGenerator: (req) => `generation:${req.user?.id || req.ip}`,
});

// API调用限流
export const apiRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 最多1000次API调用
  message: 'API rate limit exceeded, please try again later.',
  keyGenerator: (req) => `api:${req.user?.id || req.ip}`,
});

// 输入验证中间件
export const validateInput = (req: Request, res: Response, next: NextFunction): void => {
  // 检查请求体大小
  const contentLength = parseInt(req.get('content-length') || '0');
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (contentLength > maxSize) {
    res.status(413).json({
      success: false,
      error: 'PAYLOAD_TOO_LARGE',
      message: 'Request payload too large',
    });
    return;
  }

  // 检查常见的恶意输入
  const suspiciousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi,
  ];

  const checkValue = (value: any): boolean => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    }
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(checkValue);
    }
    return false;
  };

  if (req.body && checkValue(req.body)) {
    logger.warn('Suspicious input detected', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      body: req.body,
    });
    
    res.status(400).json({
      success: false,
      error: 'INVALID_INPUT',
      message: 'Invalid input detected',
    });
    return;
  }

  next();
};

// IP白名单中间件
export const ipWhitelist = (whitelist: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const clientIp = req.ip;
    
    if (!whitelist.includes(clientIp)) {
      logger.warn('IP not in whitelist', {
        ip: clientIp,
        userAgent: req.get('User-Agent'),
        url: req.url,
      });
      
      res.status(403).json({
        success: false,
        error: 'IP_NOT_ALLOWED',
        message: 'Access denied',
      });
      return;
    }
    
    next();
  };
};

// 用户代理检查中间件
export const userAgentCheck = (req: Request, res: Response, next: NextFunction): void => {
  const userAgent = req.get('User-Agent');
  
  if (!userAgent) {
    logger.warn('Missing User-Agent header', {
      ip: req.ip,
      url: req.url,
    });
    
    res.status(400).json({
      success: false,
      error: 'MISSING_USER_AGENT',
      message: 'User-Agent header is required',
    });
    return;
  }

  // 检查是否为已知的恶意用户代理
  const maliciousPatterns = [
    /sqlmap/i,
    /nikto/i,
    /nessus/i,
    /openvas/i,
    /nmap/i,
  ];

  if (maliciousPatterns.some(pattern => pattern.test(userAgent))) {
    logger.warn('Malicious User-Agent detected', {
      ip: req.ip,
      userAgent,
      url: req.url,
    });
    
    res.status(403).json({
      success: false,
      error: 'MALICIOUS_USER_AGENT',
      message: 'Access denied',
    });
    return;
  }

  next();
};

// CSRF保护中间件
export const csrfProtection = (req: Request, res: Response, next: NextFunction): void => {
  // 对于状态改变的请求，检查CSRF token
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
    const token = req.get('X-CSRF-Token') || req.body._csrf;
    const sessionToken = req.session?.csrfToken;
    
    if (!token || !sessionToken || token !== sessionToken) {
      logger.warn('CSRF token mismatch', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.url,
        method: req.method,
      });
      
      res.status(403).json({
        success: false,
        error: 'CSRF_TOKEN_MISMATCH',
        message: 'CSRF token validation failed',
      });
      return;
    }
  }
  
  next();
};

// 请求日志中间件
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // 记录请求开始
  logger.info('Request started', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
  });

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userId: req.user?.id,
    });

    // 记录到数据库（异步，不阻塞响应）
    recordApiUsage(req, res, duration).catch(error => {
      logger.error('Failed to record API usage:', error);
    });
  });

  next();
};

// 记录API使用情况
async function recordApiUsage(req: Request, res: Response, duration: number): Promise<void> {
  try {
    const { prisma } = await import('@/utils/database');
    
    await prisma.apiUsage.create({
      data: {
        userId: req.user?.id,
        endpoint: req.route?.path || req.url,
        method: req.method,
        statusCode: res.statusCode,
        responseTime: duration,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip,
      },
    });
  } catch (error) {
    // 忽略记录错误，不影响主要功能
    logger.error('Failed to record API usage:', error);
  }
}

// 内容过滤中间件
export const contentFilter = (req: Request, res: Response, next: NextFunction): void => {
  if (req.body && req.body.prompt) {
    const prompt = req.body.prompt.toLowerCase();
    
    // 检查敏感内容
    const sensitiveKeywords = [
      'password',
      'credit card',
      'social security',
      'bank account',
      // 添加更多敏感词
    ];

    const containsSensitive = sensitiveKeywords.some(keyword => 
      prompt.includes(keyword)
    );

    if (containsSensitive) {
      logger.warn('Sensitive content detected in prompt', {
        ip: req.ip,
        userId: req.user?.id,
        prompt: prompt.substring(0, 100), // 只记录前100个字符
      });
      
      res.status(400).json({
        success: false,
        error: 'SENSITIVE_CONTENT',
        message: 'Prompt contains sensitive content',
      });
      return;
    }
  }

  next();
};

// 并发请求限制中间件
export const concurrencyLimit = (maxConcurrent: number) => {
  const activeRequests = new Map<string, number>();

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.id || req.ip;
    const current = activeRequests.get(userId) || 0;

    if (current >= maxConcurrent) {
      res.status(429).json({
        success: false,
        error: 'CONCURRENCY_LIMIT_EXCEEDED',
        message: 'Too many concurrent requests',
      });
      return;
    }

    // 增加计数
    activeRequests.set(userId, current + 1);

    // 请求完成时减少计数
    res.on('finish', () => {
      const newCount = (activeRequests.get(userId) || 1) - 1;
      if (newCount <= 0) {
        activeRequests.delete(userId);
      } else {
        activeRequests.set(userId, newCount);
      }
    });

    next();
  };
};

export default {
  createRateLimit,
  loginRateLimit,
  registerRateLimit,
  generationRateLimit,
  apiRateLimit,
  validateInput,
  ipWhitelist,
  userAgentCheck,
  csrfProtection,
  requestLogger,
  contentFilter,
  concurrencyLimit,
};
