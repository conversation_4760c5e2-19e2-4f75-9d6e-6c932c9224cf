# Coze Clone Platform

一个基于 MCP Chrome 的 Coze 平台平替方案，通过浏览器自动化技术实现 AI 内容生成功能。

## 🚀 特性

- 🌐 **浏览器自动化**: 使用 MCP Chrome 实现真实浏览器操作
- 🔄 **智能代理**: 自动转发用户请求到 Coze 平台
- 📱 **现代界面**: React + TypeScript 构建的响应式前端
- ⚡ **高性能**: Node.js + Express 后端服务
- 🔒 **安全可靠**: 完整的错误处理和安全机制
- 📊 **数据管理**: PostgreSQL + Redis 数据存储

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   MCP Chrome    │
│   (React)       │◄──►│   (Node.js)     │◄──►│   Extension     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Database      │    │   Coze Platform │
                       │ (PostgreSQL)    │    │   (Target)      │
                       └─────────────────┘    └─────────────────┘
```

## 📦 项目结构

```
coze-clone-platform/
├── frontend/                 # React 前端应用
│   ├── src/
│   │   ├── components/      # React 组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义 Hooks
│   │   ├── services/       # API 服务
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript 类型定义
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                  # Node.js 后端服务
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由定义
│   │   ├── utils/          # 工具函数
│   │   └── mcp/           # MCP Chrome 集成
│   ├── tests/              # 测试文件
│   └── package.json
├── docker-compose.yml        # Docker 编排文件
├── .env.example             # 环境变量示例
└── README.md
```

## 🛠️ 技术栈

### 前端
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全
- **Vite** - 构建工具
- **Ant Design** - UI 组件库
- **Axios** - HTTP 客户端
- **React Query** - 数据获取和缓存

### 后端
- **Node.js** - 运行时环境
- **Express** - Web 框架
- **TypeScript** - 类型安全
- **Prisma** - ORM 数据库工具
- **PostgreSQL** - 主数据库
- **Redis** - 缓存数据库
- **MCP Chrome** - 浏览器自动化

### 开发工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **Jest** - 测试框架
- **Docker** - 容器化部署

## 🚀 快速开始

### 前置要求

- Node.js 18+
- npm 或 yarn
- Chrome 浏览器
- PostgreSQL 数据库
- Redis 服务

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd coze-clone-platform
```

2. **安装依赖**
```bash
npm run install:all
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **安装 MCP Chrome 扩展**
```bash
# 下载并安装 MCP Chrome 扩展
npm install -g mcp-chrome-bridge
```

5. **启动开发服务器**
```bash
npm run dev
```

### Docker 部署

```bash
# 构建镜像
npm run docker:build

# 启动服务
npm run docker:up
```

## 📖 使用说明

1. **启动服务**: 确保 MCP Chrome 扩展已安装并连接
2. **访问界面**: 打开 http://localhost:3000
3. **输入需求**: 在输入框中描述您的需求
4. **获取结果**: 系统自动转发到 Coze 平台并返回结果

## 🔧 配置说明

### MCP Chrome 配置

在 Chrome 扩展中配置 MCP 服务器连接：

```json
{
  "mcpServers": {
    "coze-clone": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

### 环境变量

```env
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/coze_clone"
REDIS_URL="redis://localhost:6379"

# 服务配置
PORT=8000
NODE_ENV=development

# MCP Chrome 配置
MCP_CHROME_PORT=12306
MCP_CHROME_HOST=127.0.0.1

# Coze 平台配置
COZE_BASE_URL="https://space.coze.cn"
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 运行前端测试
npm run test:frontend

# 运行后端测试
npm run test:backend
```

## 📝 开发指南

### 添加新功能

1. 在 `backend/src/services/` 中添加业务逻辑
2. 在 `backend/src/controllers/` 中添加控制器
3. 在 `backend/src/routes/` 中定义路由
4. 在 `frontend/src/components/` 中添加 UI 组件

### MCP Chrome 集成

参考 `backend/src/mcp/` 目录下的示例代码，了解如何使用 MCP Chrome 进行浏览器自动化。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
