{"name": "coze-clone-frontend", "version": "1.0.0", "description": "Frontend for Coze Clone Platform", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss}", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "axios": "^1.6.2", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.7", "socket.io-client": "^4.7.4", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-hotkeys-hook": "^4.4.1", "react-dropzone": "^14.2.3", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^2.5.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-syntax-highlighter": "^15.5.11", "@types/lodash-es": "^4.17.12", "@types/file-saver": "^2.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "sass": "^1.69.5"}, "engines": {"node": ">=18.0.0"}}