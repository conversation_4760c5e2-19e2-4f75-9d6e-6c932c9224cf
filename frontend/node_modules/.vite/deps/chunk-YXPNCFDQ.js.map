{"version": 3, "sources": ["../../../../node_modules/highlight.js/lib/languages/thrift.js"], "sourcesContent": ["/*\nLanguage: Thrift\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Thrift message definition format\nWebsite: https://thrift.apache.org\nCategory: protocols\n*/\n\nfunction thrift(hljs) {\n  const BUILT_IN_TYPES = 'bool byte i16 i32 i64 double string binary';\n  return {\n    name: 'Thrift',\n    keywords: {\n      keyword:\n        'namespace const typedef struct enum service exception void oneway set list map required optional',\n      built_in:\n        BUILT_IN_TYPES,\n      literal:\n        'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'struct enum service exception',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            // hack: eating everything after the first title\n            starts: {\n              endsWithParent: true,\n              excludeEnd: true\n            }\n          })\n        ]\n      },\n      {\n        begin: '\\\\b(set|list|map)\\\\s*<',\n        end: '>',\n        keywords: BUILT_IN_TYPES,\n        contains: [ 'self' ]\n      }\n    ]\n  };\n}\n\nmodule.exports = thrift;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,OAAO,MAAM;AACpB,YAAM,iBAAiB;AACvB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SACE;AAAA,UACF,UACE;AAAA,UACF,SACE;AAAA,QACJ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY;AAAA;AAAA,gBAE5B,QAAQ;AAAA,kBACN,gBAAgB;AAAA,kBAChB,YAAY;AAAA,gBACd;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU,CAAE,MAAO;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}