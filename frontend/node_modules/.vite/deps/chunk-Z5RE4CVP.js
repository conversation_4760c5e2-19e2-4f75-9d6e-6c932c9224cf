import {
  require_xeora
} from "./chunk-CGXYZ3ID.js";
import {
  require_xml_doc
} from "./chunk-CZEOESR6.js";
import {
  require_xojo
} from "./chunk-IPMJXYCI.js";
import {
  require_xquery
} from "./chunk-FRX6SQFY.js";
import {
  require_yang
} from "./chunk-WTMBHCSP.js";
import {
  require_zig
} from "./chunk-NU7MGGF4.js";
import {
  require_core
} from "./chunk-KRCBOU3I.js";
import {
  require_vim
} from "./chunk-T4ZMQNF7.js";
import {
  require_visual_basic
} from "./chunk-AKBMHQFF.js";
import {
  require_warpscript
} from "./chunk-43YWS4SD.js";
import {
  require_wasm
} from "./chunk-XFZ7RJOD.js";
import {
  require_web_idl
} from "./chunk-JY5XHUM7.js";
import {
  require_wiki
} from "./chunk-7Y67MHQN.js";
import {
  require_wolfram
} from "./chunk-2LW2ESK2.js";
import {
  require_wren
} from "./chunk-PK6O2CUY.js";
import {
  require_unrealscript
} from "./chunk-S3T2OQNK.js";
import {
  require_uorazor
} from "./chunk-YLNMPCFE.js";
import {
  require_uri
} from "./chunk-USW3F3U2.js";
import {
  require_v
} from "./chunk-WVLMC5OD.js";
import {
  require_vala
} from "./chunk-MDHZF37C.js";
import {
  require_velocity
} from "./chunk-5TPZS742.js";
import {
  require_verilog
} from "./chunk-F3NIARX3.js";
import {
  require_vhdl
} from "./chunk-GTKDM52B.js";
import {
  require_tcl
} from "./chunk-BZ364LAZ.js";
import {
  require_textile
} from "./chunk-6VW2O3MK.js";
import {
  require_toml
} from "./chunk-GLL2I7GH.js";
import {
  require_tremor
} from "./chunk-VYT6MBRT.js";
import {
  require_tsx
} from "./chunk-MFVBCCPQ.js";
import {
  require_tt2
} from "./chunk-VFBQVEDL.js";
import {
  require_twig
} from "./chunk-6D2B52MK.js";
import {
  require_typoscript
} from "./chunk-4V4LVLUH.js";
import {
  require_swift
} from "./chunk-YWQQHYJC.js";
import {
  require_systemd
} from "./chunk-FXVFQ6Q4.js";
import {
  require_t4_cs
} from "./chunk-6DJOEGKG.js";
import {
  require_t4_vb
} from "./chunk-POPTVAFK.js";
import {
  require_t4_templating
} from "./chunk-Y5F7ODHO.js";
import {
  require_vbnet
} from "./chunk-BZFH7QTR.js";
import {
  require_tap
} from "./chunk-3DHAOGSL.js";
import {
  require_yaml
} from "./chunk-P6D4TZDG.js";
import {
  require_soy
} from "./chunk-IQ6HWLRS.js";
import {
  require_sparql
} from "./chunk-X35LECKK.js";
import {
  require_turtle
} from "./chunk-LJ66U43P.js";
import {
  require_splunk_spl
} from "./chunk-YQIVARA2.js";
import {
  require_sqf
} from "./chunk-KVSRGMR7.js";
import {
  require_squirrel
} from "./chunk-FK35PM66.js";
import {
  require_stan
} from "./chunk-GPDLI3ST.js";
import {
  require_stylus
} from "./chunk-IYOX2TRJ.js";
import {
  require_scss
} from "./chunk-MY7EHDZZ.js";
import {
  require_shell_session
} from "./chunk-LWEWYONP.js";
import {
  require_smali
} from "./chunk-MSM7JABB.js";
import {
  require_smalltalk
} from "./chunk-HRAMMQ4O.js";
import {
  require_smarty
} from "./chunk-45TV2LUP.js";
import {
  require_sml
} from "./chunk-QDADMDJP.js";
import {
  require_solidity
} from "./chunk-7ZVXXGT2.js";
import {
  require_solution_file
} from "./chunk-Y4VQ77LP.js";
import {
  require_rest
} from "./chunk-LUYM3MK4.js";
import {
  require_rip
} from "./chunk-5TWRPMSS.js";
import {
  require_roboconf
} from "./chunk-QTVBZ2NE.js";
import {
  require_robotframework
} from "./chunk-VK54DPHB.js";
import {
  require_rust
} from "./chunk-EAYKLNCZ.js";
import {
  require_sas
} from "./chunk-O2HBQD4J.js";
import {
  require_sass
} from "./chunk-SGM5V2XQ.js";
import {
  require_scala
} from "./chunk-EL2U26JW.js";
import {
  require_qore
} from "./chunk-KD5V3KJA.js";
import {
  require_qsharp
} from "./chunk-65WT6XRO.js";
import {
  require_r
} from "./chunk-ATP6P7MY.js";
import {
  require_racket
} from "./chunk-BZMQ626P.js";
import {
  require_reason
} from "./chunk-UJEO4HLU.js";
import {
  require_regex
} from "./chunk-F66RIPG5.js";
import {
  require_rego
} from "./chunk-3EOYHRSI.js";
import {
  require_renpy
} from "./chunk-6ZHPLLVC.js";
import {
  require_pug
} from "./chunk-AZ4NFZWW.js";
import {
  require_puppet
} from "./chunk-UI4CMGOJ.js";
import {
  require_pure
} from "./chunk-XNRSJ72I.js";
import {
  require_purebasic
} from "./chunk-ZREPLBP6.js";
import {
  require_purescript
} from "./chunk-CXCLC3QI.js";
import {
  require_python
} from "./chunk-VW6CQZEB.js";
import {
  require_q
} from "./chunk-7FN2BKPR.js";
import {
  require_qml
} from "./chunk-UZ3AGVOI.js";
import {
  require_powerquery
} from "./chunk-2GO5QYY6.js";
import {
  require_powershell
} from "./chunk-CO4EBHGP.js";
import {
  require_processing
} from "./chunk-45MPU7MI.js";
import {
  require_prolog
} from "./chunk-CZYTAWHN.js";
import {
  require_promql
} from "./chunk-NR6LYN4C.js";
import {
  require_properties
} from "./chunk-765FDOQB.js";
import {
  require_protobuf
} from "./chunk-BQKG2LLC.js";
import {
  require_psl
} from "./chunk-4DW32GRS.js";
import {
  require_pascal
} from "./chunk-Q6YQWFAD.js";
import {
  require_pascaligo
} from "./chunk-DSPSQLNZ.js";
import {
  require_pcaxis
} from "./chunk-5TWIXPL6.js";
import {
  require_peoplecode
} from "./chunk-G4Z74VLJ.js";
import {
  require_perl
} from "./chunk-76VTHB4A.js";
import {
  require_php_extras
} from "./chunk-APR6FWGN.js";
import {
  require_phpdoc
} from "./chunk-JRAV23UG.js";
import {
  require_plsql
} from "./chunk-RRPFQPGA.js";
import {
  require_nsis
} from "./chunk-DIQ2BNBK.js";
import {
  require_objectivec
} from "./chunk-LF36NTJM.js";
import {
  require_ocaml
} from "./chunk-STYQTOXD.js";
import {
  require_opencl
} from "./chunk-6TBQSY75.js";
import {
  require_openqasm
} from "./chunk-CHSJILFL.js";
import {
  require_oz
} from "./chunk-BSMJPHM2.js";
import {
  require_parigp
} from "./chunk-4JH5YCR4.js";
import {
  require_parser
} from "./chunk-A6X3UT3V.js";
import {
  require_nand2tetris_hdl
} from "./chunk-6EO2O3OF.js";
import {
  require_naniscript
} from "./chunk-UYCRJ75R.js";
import {
  require_nasm
} from "./chunk-7XEVGMLK.js";
import {
  require_neon
} from "./chunk-XTVLMWKR.js";
import {
  require_nevod
} from "./chunk-RMZUT6CR.js";
import {
  require_nginx
} from "./chunk-6F7KSJPJ.js";
import {
  require_nim
} from "./chunk-RXAKH6R2.js";
import {
  require_nix
} from "./chunk-KH6XDYGB.js";
import {
  require_mel
} from "./chunk-XAZOZCAG.js";
import {
  require_mermaid
} from "./chunk-4KPNH32P.js";
import {
  require_mizar
} from "./chunk-7U3XCWQH.js";
import {
  require_mongodb
} from "./chunk-MKS5JGTL.js";
import {
  require_monkey
} from "./chunk-XHJSSOU5.js";
import {
  require_moonscript
} from "./chunk-U4A47GIO.js";
import {
  require_n1ql
} from "./chunk-MSTFZUZK.js";
import {
  require_n4js
} from "./chunk-DAFU2OT4.js";
import {
  require_log
} from "./chunk-RTNUPQ34.js";
import {
  require_lolcode
} from "./chunk-PXXNXXHA.js";
import {
  require_magma
} from "./chunk-3QMSEPFL.js";
import {
  require_makefile
} from "./chunk-B7J6EPXD.js";
import {
  require_markdown
} from "./chunk-CTIRX7HA.js";
import {
  require_matlab
} from "./chunk-EA4FBSYI.js";
import {
  require_maxscript
} from "./chunk-B24YSTWZ.js";
import {
  require_latte
} from "./chunk-5KDE5DUY.js";
import {
  require_less
} from "./chunk-VDPPJTMB.js";
import {
  require_lilypond
} from "./chunk-CSNYG3FH.js";
import {
  require_scheme
} from "./chunk-OS2IU62Z.js";
import {
  require_liquid
} from "./chunk-AKN3P6F3.js";
import {
  require_lisp
} from "./chunk-JROSZKY7.js";
import {
  require_livescript
} from "./chunk-OS52DSM4.js";
import {
  require_llvm
} from "./chunk-4U5PSKSB.js";
import {
  require_julia
} from "./chunk-GR44N4N2.js";
import {
  require_keepalived
} from "./chunk-E5VL3Q7Y.js";
import {
  require_keyman
} from "./chunk-7EDD7DAC.js";
import {
  require_kotlin
} from "./chunk-XHC32SFP.js";
import {
  require_kumir
} from "./chunk-V3M5CITU.js";
import {
  require_kusto
} from "./chunk-4X5R6XDE.js";
import {
  require_latex
} from "./chunk-NJ3F7BXL.js";
import {
  require_php
} from "./chunk-ENNK25GH.js";
import {
  require_js_templates
} from "./chunk-GWMIVCMF.js";
import {
  require_jsdoc
} from "./chunk-LTZO3RTZ.js";
import {
  require_typescript
} from "./chunk-7OCJJI4L.js";
import {
  require_json5
} from "./chunk-2CL2UHC3.js";
import {
  require_jsonp
} from "./chunk-HBGGXBGU.js";
import {
  require_json
} from "./chunk-CU4W7BHS.js";
import {
  require_jsstacktrace
} from "./chunk-SXQYG57K.js";
import {
  require_jsx
} from "./chunk-GHJ4OCTF.js";
import {
  require_javadoc
} from "./chunk-YK54373U.js";
import {
  require_javadoclike
} from "./chunk-6SEVTNA3.js";
import {
  require_javastacktrace
} from "./chunk-UCLFPJQO.js";
import {
  require_jexl
} from "./chunk-NTVVH3AI.js";
import {
  require_jolie
} from "./chunk-GILS7EBW.js";
import {
  require_jq
} from "./chunk-BSSF67KQ.js";
import {
  require_js_extras
} from "./chunk-SEONIW5Y.js";
import {
  require_idris
} from "./chunk-P25TIKOQ.js";
import {
  require_iecst
} from "./chunk-FKDVD5UB.js";
import {
  require_ignore
} from "./chunk-EMOBR2M7.js";
import {
  require_inform7
} from "./chunk-W6HFKD43.js";
import {
  require_ini
} from "./chunk-HSVX3LCJ.js";
import {
  require_io
} from "./chunk-NJUA2XGI.js";
import {
  require_j
} from "./chunk-RELPITAI.js";
import {
  require_java
} from "./chunk-EXEPMIPH.js";
import {
  require_hlsl
} from "./chunk-QUOTZCHZ.js";
import {
  require_hoon
} from "./chunk-6WUUKD2J.js";
import {
  require_hpkp
} from "./chunk-MGJS722L.js";
import {
  require_hsts
} from "./chunk-ZZKRY6L4.js";
import {
  require_http
} from "./chunk-VVTFDNEN.js";
import {
  require_ichigojam
} from "./chunk-PRM7RDWV.js";
import {
  require_icon
} from "./chunk-XEGZEN5F.js";
import {
  require_icu_message_format
} from "./chunk-OJWRYUFX.js";
import {
  require_go
} from "./chunk-GV6H2WNJ.js";
import {
  require_graphql
} from "./chunk-SABBXFQS.js";
import {
  require_groovy
} from "./chunk-K77T3GQX.js";
import {
  require_haml
} from "./chunk-DDTTSS76.js";
import {
  require_handlebars
} from "./chunk-O7FNJEOJ.js";
import {
  require_haskell
} from "./chunk-UV7STVBL.js";
import {
  require_haxe
} from "./chunk-LOPQRYMN.js";
import {
  require_hcl
} from "./chunk-EPP55VCD.js";
import {
  require_gdscript
} from "./chunk-G7AI4MZQ.js";
import {
  require_gedcom
} from "./chunk-X3YOM5LK.js";
import {
  require_gherkin
} from "./chunk-ITCX4FLM.js";
import {
  require_git
} from "./chunk-TUOX3R7R.js";
import {
  require_glsl
} from "./chunk-2M7KUWD6.js";
import {
  require_gml
} from "./chunk-AJCVBFJX.js";
import {
  require_gn
} from "./chunk-OXGP2HJV.js";
import {
  require_go_module
} from "./chunk-GAVSCK47.js";
import {
  require_false
} from "./chunk-O4KVA3VX.js";
import {
  require_firestore_security_rules
} from "./chunk-FYENGE4X.js";
import {
  require_flow
} from "./chunk-ERQB6A22.js";
import {
  require_fortran
} from "./chunk-4VOJ74LJ.js";
import {
  require_fsharp
} from "./chunk-VUFITYEG.js";
import {
  require_ftl
} from "./chunk-A6L5GV6E.js";
import {
  require_gap
} from "./chunk-5JWRATUJ.js";
import {
  require_gcode
} from "./chunk-KO6JM26X.js";
import {
  require_elixir
} from "./chunk-IMPHPQ3G.js";
import {
  require_elm
} from "./chunk-ZWJPADF7.js";
import {
  require_erb
} from "./chunk-XLROP5GS.js";
import {
  require_erlang
} from "./chunk-EGPJHECK.js";
import {
  require_etlua
} from "./chunk-IYAP56GE.js";
import {
  require_lua
} from "./chunk-CTVHYUL4.js";
import {
  require_excel_formula
} from "./chunk-BRSDLNVS.js";
import {
  require_factor
} from "./chunk-74XOG5TU.js";
import {
  require_django
} from "./chunk-6ODZ3XPV.js";
import {
  require_dns_zone_file
} from "./chunk-DC6AVCZA.js";
import {
  require_docker
} from "./chunk-IKF6J7AN.js";
import {
  require_dot
} from "./chunk-67KW5GWR.js";
import {
  require_ebnf
} from "./chunk-3DYLAXNT.js";
import {
  require_editorconfig
} from "./chunk-6RQLOW2B.js";
import {
  require_eiffel
} from "./chunk-CTY6MCY2.js";
import {
  require_ejs
} from "./chunk-2IDJ4CSW.js";
import {
  require_cypher
} from "./chunk-YJ7JHWSH.js";
import {
  require_d
} from "./chunk-7EH36V22.js";
import {
  require_dart
} from "./chunk-4HH53M4I.js";
import {
  require_dataweave
} from "./chunk-L3CT2QQC.js";
import {
  require_dax
} from "./chunk-DMLSTMFV.js";
import {
  require_dhall
} from "./chunk-HHSUZCYF.js";
import {
  require_diff
} from "./chunk-H2BS57JK.js";
import {
  require_markup_templating
} from "./chunk-5ZQTIMC2.js";
import {
  require_coq
} from "./chunk-MBYG4HFF.js";
import {
  require_crystal
} from "./chunk-MGYVARS7.js";
import {
  require_ruby
} from "./chunk-UV3AVMAZ.js";
import {
  require_cshtml
} from "./chunk-2KI52GUB.js";
import {
  require_csp
} from "./chunk-QQSXX6A7.js";
import {
  require_css_extras
} from "./chunk-5GMM4H7I.js";
import {
  require_csv
} from "./chunk-MCVAO26D.js";
import {
  require_chaiscript
} from "./chunk-FE6MUEIE.js";
import {
  require_cil
} from "./chunk-AKGR6PAM.js";
import {
  require_clojure
} from "./chunk-KDHKEQUN.js";
import {
  require_cmake
} from "./chunk-AKDT4TU6.js";
import {
  require_cobol
} from "./chunk-SI2YEDAS.js";
import {
  require_coffeescript
} from "./chunk-INLMF4I7.js";
import {
  require_concurnas
} from "./chunk-GJR3RTZG.js";
import {
  require_birb
} from "./chunk-XOPX3P2H.js";
import {
  require_bison
} from "./chunk-QM4SAI2I.js";
import {
  require_bnf
} from "./chunk-3O23MUDZ.js";
import {
  require_brainfuck
} from "./chunk-KVDMMU5N.js";
import {
  require_brightscript
} from "./chunk-4RAGK6UJ.js";
import {
  require_bro
} from "./chunk-KUKR6OO6.js";
import {
  require_bsl
} from "./chunk-SKZAKSDH.js";
import {
  require_cfscript
} from "./chunk-ZFWTRW3Q.js";
import {
  require_autoit
} from "./chunk-YGC4JLHI.js";
import {
  require_avisynth
} from "./chunk-6IDFH52H.js";
import {
  require_avro_idl
} from "./chunk-SI3PZHPZ.js";
import {
  require_bash
} from "./chunk-OWPDOHCP.js";
import {
  require_basic
} from "./chunk-7DVWVSX5.js";
import {
  require_batch
} from "./chunk-GNYFS6IJ.js";
import {
  require_bbcode
} from "./chunk-LWWEILIY.js";
import {
  require_bicep
} from "./chunk-C66FADAT.js";
import {
  require_arduino
} from "./chunk-XUPOH54V.js";
import {
  require_arff
} from "./chunk-RG3SUVG4.js";
import {
  require_asciidoc
} from "./chunk-7KVJCSI2.js";
import {
  require_asm6502
} from "./chunk-RM4ZCRI3.js";
import {
  require_asmatmel
} from "./chunk-DI6GWJFR.js";
import {
  require_aspnet
} from "./chunk-6BHJH5ZN.js";
import {
  require_csharp
} from "./chunk-ZM7DG2D7.js";
import {
  require_autohotkey
} from "./chunk-B6BZHEFJ.js";
import {
  require_apacheconf
} from "./chunk-ZFFFG3UV.js";
import {
  require_apex
} from "./chunk-LN7VMIUR.js";
import {
  require_sql
} from "./chunk-D32OYQHV.js";
import {
  require_apl
} from "./chunk-SGWFJIXX.js";
import {
  require_applescript
} from "./chunk-MTEQKEPA.js";
import {
  require_aql
} from "./chunk-H4V7IDWA.js";
import {
  require_cpp
} from "./chunk-JRAXVMD7.js";
import {
  require_c
} from "./chunk-5WT7Y5LF.js";
import {
  require_abap
} from "./chunk-PVNE4ABL.js";
import {
  require_abnf
} from "./chunk-HC3SPAQT.js";
import {
  require_actionscript
} from "./chunk-L4OWVRZV.js";
import {
  require_ada
} from "./chunk-HCJ4EKN5.js";
import {
  require_agda
} from "./chunk-AXTHRCU4.js";
import {
  require_al
} from "./chunk-7C6PYGGX.js";
import {
  require_antlr4
} from "./chunk-PKGJ7SD3.js";
import {
  __commonJS
} from "./chunk-WOOG5QLI.js";

// ../node_modules/refractor/index.js
var require_refractor = __commonJS({
  "../node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-Z5RE4CVP.js.map
