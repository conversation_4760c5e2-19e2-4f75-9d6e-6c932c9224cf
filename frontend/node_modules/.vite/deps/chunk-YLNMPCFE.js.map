{"version": 3, "sources": ["../../../../node_modules/refractor/lang/uorazor.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = uorazor\nuorazor.displayName = 'uorazor'\nuorazor.aliases = []\nfunction uorazor(Prism) {\n  Prism.languages.uorazor = {\n    'comment-hash': {\n      pattern: /#.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    'comment-slash': {\n      pattern: /\\/\\/.*/,\n      alias: 'comment',\n      greedy: true\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      inside: {\n        punctuation: /^['\"]|['\"]$/\n      },\n      greedy: true\n    },\n    'source-layers': {\n      pattern:\n        /\\b(?:arms|backpack|blue|bracelet|cancel|clear|cloak|criminal|earrings|enemy|facialhair|friend|friendly|gloves|gray|grey|ground|hair|head|innerlegs|innertorso|innocent|lefthand|middletorso|murderer|neck|nonfriendly|onehandedsecondary|outerlegs|outertorso|pants|red|righthand|ring|self|shirt|shoes|talisman|waist)\\b/i,\n      alias: 'function'\n    },\n    'source-commands': {\n      pattern:\n        /\\b(?:alliance|attack|cast|clearall|clearignore|clearjournal|clearlist|clearsysmsg|createlist|createtimer|dclick|dclicktype|dclickvar|dress|dressconfig|drop|droprelloc|emote|getlabel|guild|gumpclose|gumpresponse|hotkey|ignore|lasttarget|lift|lifttype|menu|menuresponse|msg|org|organize|organizer|overhead|pause|poplist|potion|promptresponse|pushlist|removelist|removetimer|rename|restock|say|scav|scavenger|script|setability|setlasttarget|setskill|settimer|setvar|sysmsg|target|targetloc|targetrelloc|targettype|undress|unignore|unsetvar|useobject|useonce|useskill|usetype|virtue|wait|waitforgump|waitformenu|waitforprompt|waitforstat|waitforsysmsg|waitfortarget|walk|wfsysmsg|wft|whisper|yell)\\b/,\n      alias: 'function'\n    },\n    'tag-name': {\n      pattern: /(^\\{%-?\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    delimiter: {\n      pattern: /^\\{[{%]-?|-?[%}]\\}$/,\n      alias: 'punctuation'\n    },\n    function:\n      /\\b(?:atlist|close|closest|count|counter|counttype|dead|dex|diffhits|diffmana|diffstam|diffweight|find|findbuff|finddebuff|findlayer|findtype|findtypelist|followers|gumpexists|hidden|hits|hp|hue|human|humanoid|ingump|inlist|insysmessage|insysmsg|int|invul|lhandempty|list|listexists|mana|maxhits|maxhp|maxmana|maxstam|maxweight|monster|mounted|name|next|noto|paralyzed|poisoned|position|prev|previous|queued|rand|random|rhandempty|skill|stam|str|targetexists|timer|timerexists|varexist|warmode|weight)\\b/,\n    keyword:\n      /\\b(?:and|as|break|continue|else|elseif|endfor|endif|endwhile|for|if|loop|not|or|replay|stop|while)\\b/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n    operator: [\n      {\n        pattern:\n          /(\\s)(?:and|b-and|b-or|b-xor|ends with|in|is|matches|not|or|same as|starts with)(?=\\s)/,\n        lookbehind: true\n      },\n      /[=<>]=?|!=|\\*\\*?|\\/\\/?|\\?:?|[-+~%|]/\n    ],\n    punctuation: /[()\\[\\]{}:.,]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA,QACxB,gBAAgB;AAAA,UACd,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,QACA,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,UACA,QAAQ;AAAA,QACV;AAAA,QACA,iBAAiB;AAAA,UACf,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UACE;AAAA,QACF,SACE;AAAA,QACF,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,UACR;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}