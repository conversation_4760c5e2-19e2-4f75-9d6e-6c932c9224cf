{"version": 3, "sources": ["../../../../node_modules/refractor/lang/splunk-spl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = splunkSpl\nsplunkSpl.displayName = 'splunkSpl'\nsplunkSpl.aliases = []\nfunction splunkSpl(Prism) {\n  Prism.languages['splunk-spl'] = {\n    comment: /`comment\\(\"(?:\\\\.|[^\\\\\"])*\"\\)`/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\"])*\"/,\n      greedy: true\n    },\n    // https://docs.splunk.com/Documentation/Splunk/7.3.0/SearchReference/ListOfSearchCommands\n    keyword:\n      /\\b(?:abstract|accum|addcoltotals|addinfo|addtotals|analyzefields|anomalies|anomalousvalue|anomalydetection|append|appendcols|appendcsv|appendlookup|appendpipe|arules|associate|audit|autoregress|bin|bucket|bucketdir|chart|cluster|cofilter|collect|concurrency|contingency|convert|correlate|datamodel|dbinspect|dedup|delete|delta|diff|erex|eval|eventcount|eventstats|extract|fieldformat|fields|fieldsummary|filldown|fillnull|findtypes|folderize|foreach|format|from|gauge|gentimes|geom|geomfilter|geostats|head|highlight|history|iconify|input|inputcsv|inputlookup|iplocation|join|kmeans|kv|kvform|loadjob|localize|localop|lookup|makecontinuous|makemv|makeresults|map|mcollect|metadata|metasearch|meventcollect|mstats|multikv|multisearch|mvcombine|mvexpand|nomv|outlier|outputcsv|outputlookup|outputtext|overlap|pivot|predict|rangemap|rare|regex|relevancy|reltime|rename|replace|rest|return|reverse|rex|rtorder|run|savedsearch|script|scrub|search|searchtxn|selfjoin|sendemail|set|setfields|sichart|sirare|sistats|sitimechart|sitop|sort|spath|stats|strcat|streamstats|table|tags|tail|timechart|timewrap|top|transaction|transpose|trendline|tscollect|tstats|typeahead|typelearner|typer|union|uniq|untable|where|x11|xmlkv|xmlunescape|xpath|xyseries)\\b/i,\n    'operator-word': {\n      pattern: /\\b(?:and|as|by|not|or|xor)\\b/i,\n      alias: 'operator'\n    },\n    function: /\\b\\w+(?=\\s*\\()/,\n    property: /\\b\\w+(?=\\s*=(?!=))/,\n    date: {\n      // MM/DD/YYYY(:HH:MM:SS)?\n      pattern: /\\b\\d{1,2}\\/\\d{1,2}\\/\\d{1,4}(?:(?::\\d{1,2}){3})?\\b/,\n      alias: 'number'\n    },\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    boolean: /\\b(?:f|false|t|true)\\b/i,\n    operator: /[<>=]=?|[-+*/%|]/,\n    punctuation: /[()[\\],]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC;AACrB,aAAS,UAAU,OAAO;AACxB,YAAM,UAAU,YAAY,IAAI;AAAA,QAC9B,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA;AAAA,QAEA,SACE;AAAA,QACF,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA;AAAA,UAEJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}