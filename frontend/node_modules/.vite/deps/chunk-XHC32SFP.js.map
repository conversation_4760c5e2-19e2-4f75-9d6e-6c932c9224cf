{"version": 3, "sources": ["../../../../node_modules/refractor/lang/kotlin.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = kotlin\nkotlin.displayName = 'kotlin'\nkotlin.aliases = ['kt', 'kts']\nfunction kotlin(Prism) {\n  ;(function (Prism) {\n    Prism.languages.kotlin = Prism.languages.extend('clike', {\n      keyword: {\n        // The lookbehind prevents wrong highlighting of e.g. kotlin.properties.get\n        pattern:\n          /(^|[^.])\\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\\b/,\n        lookbehind: true\n      },\n      function: [\n        {\n          pattern: /(?:`[^\\r\\n`]+`|\\b\\w+)(?=\\s*\\()/,\n          greedy: true\n        },\n        {\n          pattern: /(\\.)(?:`[^\\r\\n`]+`|\\w+)(?=\\s*\\{)/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      number:\n        /\\b(?:0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\\d+(?:_\\d+)*(?:\\.\\d+(?:_\\d+)*)?(?:[eE][+-]?\\d+(?:_\\d+)*)?[fFL]?)\\b/,\n      operator:\n        /\\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\\/*%<>]=?|[?:]:?|\\.\\.|&&|\\|\\||\\b(?:and|inv|or|shl|shr|ushr|xor)\\b/\n    })\n    delete Prism.languages.kotlin['class-name']\n    var interpolationInside = {\n      'interpolation-punctuation': {\n        pattern: /^\\$\\{?|\\}$/,\n        alias: 'punctuation'\n      },\n      expression: {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.kotlin\n      }\n    }\n    Prism.languages.insertBefore('kotlin', 'string', {\n      // https://kotlinlang.org/spec/expressions.html#string-interpolation-expressions\n      'string-literal': [\n        {\n          pattern: /\"\"\"(?:[^$]|\\$(?:(?!\\{)|\\{[^{}]*\\}))*?\"\"\"/,\n          alias: 'multiline',\n          inside: {\n            interpolation: {\n              pattern: /\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n              inside: interpolationInside\n            },\n            string: /[\\s\\S]+/\n          }\n        },\n        {\n          pattern: /\"(?:[^\"\\\\\\r\\n$]|\\\\.|\\$(?:(?!\\{)|\\{[^{}]*\\}))*\"/,\n          alias: 'singleline',\n          inside: {\n            interpolation: {\n              pattern: /((?:^|[^\\\\])(?:\\\\{2})*)\\$(?:[a-z_]\\w*|\\{[^{}]*\\})/i,\n              lookbehind: true,\n              inside: interpolationInside\n            },\n            string: /[\\s\\S]+/\n          }\n        }\n      ],\n      char: {\n        // https://kotlinlang.org/spec/expressions.html#character-literals\n        pattern: /'(?:[^'\\\\\\r\\n]|\\\\(?:.|u[a-fA-F0-9]{0,4}))'/,\n        greedy: true\n      }\n    })\n    delete Prism.languages.kotlin['string']\n    Prism.languages.insertBefore('kotlin', 'keyword', {\n      annotation: {\n        pattern: /\\B@(?:\\w+:)?(?:[A-Z]\\w*|\\[[^\\]]+\\])/,\n        alias: 'builtin'\n      }\n    })\n    Prism.languages.insertBefore('kotlin', 'function', {\n      label: {\n        pattern: /\\b\\w+@|@\\w+\\b/,\n        alias: 'symbol'\n      }\n    })\n    Prism.languages.kt = Prism.languages.kotlin\n    Prism.languages.kts = Prism.languages.kotlin\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,MAAM,KAAK;AAC7B,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,SAASA,OAAM,UAAU,OAAO,SAAS;AAAA,UACvD,SAAS;AAAA;AAAA,YAEP,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,UAAU;AAAA,YACR;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,QACE;AAAA,UACF,UACE;AAAA,QACJ,CAAC;AACD,eAAOA,OAAM,UAAU,OAAO,YAAY;AAC1C,YAAI,sBAAsB;AAAA,UACxB,6BAA6B;AAAA,YAC3B,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,UAAU,UAAU;AAAA;AAAA,UAE/C,kBAAkB;AAAA,YAChB;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,eAAe;AAAA,kBACb,SAAS;AAAA,kBACT,QAAQ;AAAA,gBACV;AAAA,gBACA,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,eAAe;AAAA,kBACb,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,gBACV;AAAA,gBACA,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,MAAM;AAAA;AAAA,YAEJ,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,eAAOA,OAAM,UAAU,OAAO,QAAQ;AACtC,QAAAA,OAAM,UAAU,aAAa,UAAU,WAAW;AAAA,UAChD,YAAY;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,UAAU,YAAY;AAAA,UACjD,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AACrC,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AAAA,MACxC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}