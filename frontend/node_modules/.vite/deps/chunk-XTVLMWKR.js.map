{"version": 3, "sources": ["../../../../node_modules/refractor/lang/neon.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = neon\nneon.displayName = 'neon'\nneon.aliases = []\nfunction neon(Prism) {\n  Prism.languages.neon = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    datetime: {\n      pattern:\n        /(^|[[{(=:,\\s])\\d\\d\\d\\d-\\d\\d?-\\d\\d?(?:(?:[Tt]| +)\\d\\d?:\\d\\d:\\d\\d(?:\\.\\d*)? *(?:Z|[-+]\\d\\d?(?::?\\d\\d)?)?)?(?=$|[\\]}),\\s])/,\n      lookbehind: true,\n      alias: 'number'\n    },\n    key: {\n      pattern: /(^|[[{(,\\s])[^,:=[\\]{}()'\"\\s]+(?=\\s*:(?:$|[\\]}),\\s])|\\s*=)/,\n      lookbehind: true,\n      alias: 'atrule'\n    },\n    number: {\n      pattern:\n        /(^|[[{(=:,\\s])[+-]?(?:0x[\\da-fA-F]+|0o[0-7]+|0b[01]+|(?:\\d+(?:\\.\\d*)?|\\.?\\d+)(?:[eE][+-]?\\d+)?)(?=$|[\\]}),:=\\s])/,\n      lookbehind: true\n    },\n    boolean: {\n      pattern: /(^|[[{(=:,\\s])(?:false|no|true|yes)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true\n    },\n    null: {\n      pattern: /(^|[[{(=:,\\s])(?:null)(?=$|[\\]}),:=\\s])/i,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    string: {\n      pattern:\n        /(^|[[{(=:,\\s])(?:('''|\"\"\")\\r?\\n(?:(?:[^\\r\\n]|\\r?\\n(?![\\t ]*\\2))*\\r?\\n)?[\\t ]*\\2|'[^'\\r\\n]*'|\"(?:\\\\.|[^\\\\\"\\r\\n])*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    literal: {\n      pattern:\n        /(^|[[{(=:,\\s])(?:[^#\"',:=[\\]{}()\\s`-]|[:-][^\"',=[\\]{}()\\s])(?:[^,:=\\]})(\\s]|:(?![\\s,\\]})]|$)|[ \\t]+[^#,:=\\]})(\\s])*/,\n      lookbehind: true,\n      alias: 'string'\n    },\n    punctuation: /[,:=[\\]{}()-]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO;AAAA,QACrB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,KAAK;AAAA,UACH,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}