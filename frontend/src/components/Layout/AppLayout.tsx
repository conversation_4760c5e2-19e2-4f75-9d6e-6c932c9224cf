import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Button, Space, Typography, Tooltip, message } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  HomeOutlined,
  PlusOutlined,
  HistoryOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  QuestionCircleOutlined,
  WifiOutlined,
  DisconnectOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { useWebSocket } from '@/hooks/useWebSocket';
import apiClient from '@/services/api';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mcpStatus, setMcpStatus] = useState<'unknown' | 'connected' | 'disconnected' | 'error'>('unknown');
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { connected, connectionStatus, lastMessage } = useWebSocket();

  // 检查 MCP 连接状态
  useEffect(() => {
    const checkMcpStatus = async () => {
      try {
        const response = await apiClient.get('/mcp/status');
        setMcpStatus(response.data.status);
      } catch (error) {
        setMcpStatus('disconnected');
      }
    };

    checkMcpStatus();
    const interval = setInterval(checkMcpStatus, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, []);

  // 监听 WebSocket 消息
  useEffect(() => {
    if (lastMessage) {
      console.log('Received WebSocket message:', lastMessage);
    }
  }, [lastMessage]);

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/generation',
      icon: <PlusOutlined />,
      label: '内容生成',
    },
    {
      key: '/history',
      icon: <HistoryOutlined />,
      label: '历史记录',
    },
  ];

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
      onClick: () => window.open('/help', '_blank'),
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 获取连接状态图标和颜色
  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <WifiOutlined style={{ color: '#52c41a' }} />;
      case 'connecting':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'disconnected':
        return <DisconnectOutlined style={{ color: '#ff4d4f' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <DisconnectOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getMcpStatusIcon = () => {
    switch (mcpStatus) {
      case 'connected':
        return <WifiOutlined style={{ color: '#52c41a' }} />;
      case 'disconnected':
        return <DisconnectOutlined style={{ color: '#ff4d4f' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <LoadingOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getConnectionStatusText = () => {
    const wsStatus = connectionStatus === 'connected' ? '已连接' : '连接断开';
    const mcpStatusText = mcpStatus === 'connected' ? 'MCP已连接' : 'MCP断开';
    return `WebSocket: ${wsStatus} | ${mcpStatusText}`;
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          background: '#fff',
          borderRight: '1px solid #f0f0f0',
        }}
      >
        {/* Logo */}
        <div
          style={{
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'flex-start',
            padding: collapsed ? 0 : '0 24px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          {!collapsed && (
            <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
              Coze Clone
            </Text>
          )}
          {collapsed && (
            <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
              CC
            </Text>
          )}
        </div>

        {/* 菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>

      <Layout>
        {/* 顶部导航 */}
        <Header
          style={{
            padding: '0 24px',
            background: '#fff',
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          {/* 左侧 */}
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: 16 }}
            />
          </Space>

          {/* 右侧 */}
          <Space>
            {/* 连接状态指示器 */}
            <Tooltip title={getConnectionStatusText()}>
              <Space>
                {getConnectionIcon()}
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {connectionStatus === 'connected' ? '已连接' : '连接断开'}
                </Text>
              </Space>
            </Tooltip>

            {/* MCP 状态指示器 */}
            <Tooltip title={`MCP Chrome: ${mcpStatus}`}>
              <Space>
                {getMcpStatusIcon()}
                <Text type="secondary" style={{ fontSize: 12 }}>
                  MCP
                </Text>
              </Space>
            </Tooltip>

            {/* 通知 */}
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: 16 }}
              />
            </Badge>

            {/* 用户菜单 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  size="small"
                  src={user?.avatar}
                  icon={<UserOutlined />}
                />
                <Text>{user?.username}</Text>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 主内容区域 */}
        <Content
          style={{
            margin: 24,
            padding: 24,
            background: '#fff',
            borderRadius: 8,
            minHeight: 'calc(100vh - 112px)',
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppLayout;
