import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from '@/hooks/useAuth';
import { SocketProvider } from '@/hooks/useSocket';
import { mockUser } from './setup';

// 创建测试用的QueryClient
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

// Mock AuthProvider
const MockAuthProvider: React.FC<{ children: React.ReactNode; user?: any }> = ({ 
  children, 
  user = mockUser 
}) => {
  const mockAuthContext = {
    user,
    token: 'mock-token',
    loading: false,
    login: jest.fn().mockResolvedValue(undefined),
    register: jest.fn().mockResolvedValue(undefined),
    logout: jest.fn(),
    refreshToken: jest.fn().mockResolvedValue(undefined),
    updateUser: jest.fn().mockResolvedValue(undefined),
  };

  return (
    <AuthProvider value={mockAuthContext}>
      {children}
    </AuthProvider>
  );
};

// Mock SocketProvider
const MockSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const mockSocketContext = {
    socket: null,
    connected: true,
    joinRoom: jest.fn(),
    leaveRoom: jest.fn(),
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
  };

  return (
    <SocketProvider value={mockSocketContext}>
      {children}
    </SocketProvider>
  );
};

// 自定义渲染函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  user?: any;
  queryClient?: QueryClient;
}

const AllTheProviders: React.FC<{
  children: React.ReactNode;
  initialEntries?: string[];
  user?: any;
  queryClient?: QueryClient;
}> = ({ 
  children, 
  initialEntries = ['/'], 
  user = mockUser,
  queryClient = createTestQueryClient()
}) => {
  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider locale={zhCN}>
          <MockAuthProvider user={user}>
            <MockSocketProvider>
              {children}
            </MockSocketProvider>
          </MockAuthProvider>
        </ConfigProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries, user, queryClient, ...renderOptions } = options;

  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <AllTheProviders 
      initialEntries={initialEntries} 
      user={user}
      queryClient={queryClient}
    >
      {children}
    </AllTheProviders>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// 渲染Hook的工具函数
export const renderHook = <T,>(
  hook: () => T,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries, user, queryClient } = options;
  
  const wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <AllTheProviders 
      initialEntries={initialEntries} 
      user={user}
      queryClient={queryClient}
    >
      {children}
    </AllTheProviders>
  );

  const { renderHook: originalRenderHook } = require('@testing-library/react');
  return originalRenderHook(hook, { wrapper });
};

// 等待异步操作完成
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

// 模拟用户交互
export const mockUserInteraction = {
  type: async (element: HTMLElement, text: string) => {
    const { userEvent } = await import('@testing-library/user-event');
    const user = userEvent.setup();
    await user.clear(element);
    await user.type(element, text);
  },

  click: async (element: HTMLElement) => {
    const { userEvent } = await import('@testing-library/user-event');
    const user = userEvent.setup();
    await user.click(element);
  },

  selectOption: async (element: HTMLElement, option: string) => {
    const { userEvent } = await import('@testing-library/user-event');
    const user = userEvent.setup();
    await user.selectOptions(element, option);
  },

  upload: async (element: HTMLElement, file: File) => {
    const { userEvent } = await import('@testing-library/user-event');
    const user = userEvent.setup();
    await user.upload(element, file);
  },
};

// 创建测试文件
export const createTestFile = (
  name = 'test.txt',
  content = 'test content',
  type = 'text/plain'
): File => {
  return new File([content], name, { type });
};

// 模拟API响应延迟
export const mockApiDelay = (ms = 100) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 验证表单错误
export const expectFormError = async (container: HTMLElement, fieldName: string, errorMessage: string) => {
  const { findByText } = await import('@testing-library/react');
  const errorElement = await findByText(container, errorMessage);
  expect(errorElement).toBeInTheDocument();
};

// 验证成功消息
export const expectSuccessMessage = async (message: string) => {
  const { message: antdMessage } = await import('antd');
  expect(antdMessage.success).toHaveBeenCalledWith(message);
};

// 验证错误消息
export const expectErrorMessage = async (message: string) => {
  const { message: antdMessage } = await import('antd');
  expect(antdMessage.error).toHaveBeenCalledWith(message);
};

// 模拟路由导航
export const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// 清理导航mock
export const clearNavigateMock = () => {
  mockNavigate.mockClear();
};

// 验证导航调用
export const expectNavigation = (path: string, options?: any) => {
  if (options) {
    expect(mockNavigate).toHaveBeenCalledWith(path, options);
  } else {
    expect(mockNavigate).toHaveBeenCalledWith(path);
  }
};

// 模拟localStorage
export const mockLocalStorage = {
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// 设置localStorage mock值
export const setLocalStorageItem = (key: string, value: string) => {
  mockLocalStorage.getItem.mockImplementation((k) => k === key ? value : null);
};

// 清理localStorage mock
export const clearLocalStorageMock = () => {
  Object.values(mockLocalStorage).forEach(mock => mock.mockClear());
};

// 重新导出testing-library的所有工具
export * from '@testing-library/react';
export { customRender as render };
