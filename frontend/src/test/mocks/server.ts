import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { mockUser, mockGeneration, mockAuthResponse, createMockApiResponse } from '../setup';

// API基础URL
const API_BASE = '/api';

// Mock handlers
export const handlers = [
  // 认证相关
  rest.post(`${API_BASE}/auth/register`, (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json(createMockApiResponse(mockAuthResponse))
    );
  }),

  rest.post(`${API_BASE}/auth/login`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse(mockAuthResponse))
    );
  }),

  rest.post(`${API_BASE}/auth/logout`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse(null))
    );
  }),

  rest.post(`${API_BASE}/auth/refresh`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse(mockAuthResponse))
    );
  }),

  rest.get(`${API_BASE}/auth/me`, (req, res, ctx) => {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res(
        ctx.status(401),
        ctx.json(createMockApiResponse(null, false))
      );
    }

    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse(mockUser))
    );
  }),

  // 生成相关
  rest.post(`${API_BASE}/generation`, (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json(createMockApiResponse({
        ...mockGeneration,
        status: 'pending',
        content: '',
        completedAt: null,
      }))
    );
  }),

  rest.get(`${API_BASE}/generation/:id`, (req, res, ctx) => {
    const { id } = req.params;
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        ...mockGeneration,
        id,
      }))
    );
  }),

  rest.get(`${API_BASE}/generation`, (req, res, ctx) => {
    const page = Number(req.url.searchParams.get('page')) || 1;
    const limit = Number(req.url.searchParams.get('limit')) || 20;

    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        generations: [mockGeneration],
        total: 1,
        page,
        totalPages: 1,
      }))
    );
  }),

  rest.get(`${API_BASE}/generation/search`, (req, res, ctx) => {
    const query = req.url.searchParams.get('q');
    
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        generations: query ? [mockGeneration] : [],
        total: query ? 1 : 0,
        page: 1,
        totalPages: query ? 1 : 0,
      }))
    );
  }),

  rest.delete(`${API_BASE}/generation/:id`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse(null))
    );
  }),

  rest.get(`${API_BASE}/generation/stats`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        total: 10,
        completed: 8,
        failed: 1,
        pending: 1,
        processing: 0,
      }))
    );
  }),

  // 用户相关
  rest.get(`${API_BASE}/user/profile`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse(mockUser))
    );
  }),

  rest.put(`${API_BASE}/user/profile`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse(mockUser))
    );
  }),

  rest.get(`${API_BASE}/user/settings`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        id: 'settings-id',
        userId: mockUser.id,
        theme: 'light',
        language: 'zh-CN',
        emailNotifications: true,
        pushNotifications: true,
        soundNotifications: false,
        defaultCategory: 'article',
        defaultTemperature: 0.7,
        defaultMaxTokens: 2000,
        autoSave: true,
      }))
    );
  }),

  rest.put(`${API_BASE}/user/settings`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        id: 'settings-id',
        userId: mockUser.id,
        theme: 'light',
        language: 'zh-CN',
        emailNotifications: true,
        pushNotifications: true,
        soundNotifications: false,
        defaultCategory: 'article',
        defaultTemperature: 0.7,
        defaultMaxTokens: 2000,
        autoSave: true,
      }))
    );
  }),

  // 健康检查
  rest.get(`${API_BASE}/health`, (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(createMockApiResponse({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: 12345,
        responseTime: 50,
        version: '1.0.0',
        environment: 'test',
        services: {
          database: { status: 'healthy', responseTime: 'ok' },
          redis: { status: 'healthy', responseTime: 'ok' },
          mcpChrome: { status: 'healthy', responseTime: 'ok' },
        },
        memory: {
          used: 100,
          total: 512,
          external: 50,
        },
      }))
    );
  }),

  // 错误处理
  rest.get(`${API_BASE}/error-test`, (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json(createMockApiResponse(null, false))
    );
  }),

  // 404处理
  rest.get('*', (req, res, ctx) => {
    console.warn(`Unhandled ${req.method} request to ${req.url}`);
    return res(
      ctx.status(404),
      ctx.json(createMockApiResponse(null, false))
    );
  }),
];

// 创建服务器
export const server = setupServer(...handlers);

// 错误处理器
export const errorHandlers = {
  networkError: rest.get(`${API_BASE}/*`, (req, res, ctx) => {
    return res.networkError('Network error');
  }),

  serverError: rest.get(`${API_BASE}/*`, (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json(createMockApiResponse(null, false))
    );
  }),

  unauthorized: rest.get(`${API_BASE}/*`, (req, res, ctx) => {
    return res(
      ctx.status(401),
      ctx.json(createMockApiResponse(null, false))
    );
  }),

  rateLimited: rest.get(`${API_BASE}/*`, (req, res, ctx) => {
    return res(
      ctx.status(429),
      ctx.json(createMockApiResponse(null, false))
    );
  }),
};
