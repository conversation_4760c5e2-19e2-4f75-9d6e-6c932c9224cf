// 全局样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

// 自定义滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 代码高亮样式
.code-highlight {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  
  pre {
    margin: 0;
    background: none !important;
  }
}

// 头像上传样式
.avatar-uploader {
  .ant-upload {
    width: 104px;
    height: 104px;
    border-radius: 50%;
  }
  
  .ant-upload-select-picture-card {
    width: 104px;
    height: 104px;
    border-radius: 50%;
  }
}

// 生成结果样式
.generation-result {
  .markdown-content {
    line-height: 1.8;
    
    h1, h2, h3, h4, h5, h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
    }
    
    h1 {
      font-size: 2em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    
    h2 {
      font-size: 1.5em;
      border-bottom: 1px solid #eaecef;
      padding-bottom: 0.3em;
    }
    
    p {
      margin-bottom: 16px;
    }
    
    ul, ol {
      margin-bottom: 16px;
      padding-left: 2em;
    }
    
    li {
      margin-bottom: 4px;
    }
    
    blockquote {
      margin: 16px 0;
      padding: 0 1em;
      color: #6a737d;
      border-left: 0.25em solid #dfe2e5;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;
      
      th, td {
        padding: 8px 12px;
        border: 1px solid #dfe2e5;
        text-align: left;
      }
      
      th {
        background-color: #f6f8fa;
        font-weight: 600;
      }
    }
    
    img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
      margin: 16px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 999;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
}

// 动画效果
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// 错误状态
.error-boundary {
  padding: 40px;
  text-align: center;
  
  .error-icon {
    font-size: 64px;
    color: #ff4d4f;
    margin-bottom: 16px;
  }
  
  .error-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #262626;
  }
  
  .error-description {
    color: #8c8c8c;
    margin-bottom: 24px;
  }
}

// 自定义按钮样式
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    color: white;
  }
}

// 卡片悬停效果
.hover-card {
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

// 状态指示器
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    
    &.success {
      background-color: #52c41a;
    }
    
    &.warning {
      background-color: #faad14;
    }
    
    &.error {
      background-color: #ff4d4f;
    }
    
    &.processing {
      background-color: #1890ff;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 工具提示样式
.custom-tooltip {
  .ant-tooltip-inner {
    background-color: #001529;
    color: white;
  }
  
  .ant-tooltip-arrow::before {
    background-color: #001529;
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    background: white !important;
  }
  
  .ant-layout,
  .ant-card {
    box-shadow: none !important;
    border: none !important;
  }
}
