// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// 生成相关类型
export interface GenerationRequest {
  prompt: string;
  category?: string;
  options?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    saveToHistory?: boolean;
  };
}

export interface GenerationResponse {
  id: string;
  content: string;
  images?: string[];
  metadata?: {
    model?: string;
    tokens?: number;
    duration?: number;
    category?: string;
    title?: string;
    extractedAt?: string;
  };
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  completedAt?: string;
  error?: string;
}

export interface GenerationListResponse {
  generations: GenerationResponse[];
  total: number;
  page: number;
  totalPages: number;
}

export interface GenerationStats {
  total: number;
  completed: number;
  failed: number;
  pending: number;
  processing: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
  statusCode?: number;
}

// Socket事件类型
export interface SocketEvents {
  'generation:status': {
    id: string;
    status: GenerationResponse['status'];
    message?: string;
  };
  'generation:completed': {
    id: string;
    content: string;
    images?: string[];
    metadata?: any;
  };
  'generation:failed': {
    id: string;
    error: string;
  };
  'generation:progress': {
    id: string;
    progress: number;
    message?: string;
  };
}

// 组件Props类型
export interface BaseComponentProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

export interface LoadingProps extends BaseComponentProps {
  loading?: boolean;
  tip?: string;
  size?: 'small' | 'default' | 'large';
}

// 表单类型
export interface FormFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  rules?: any[];
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface SearchParams extends PaginationParams {
  query?: string;
  category?: string;
  status?: GenerationResponse['status'];
  startDate?: string;
  endDate?: string;
}

// 主题类型
export interface ThemeConfig {
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  borderRadius: number;
}

// 设置类型
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  autoSave: boolean;
  notifications: {
    email: boolean;
    push: boolean;
    sound: boolean;
  };
  generation: {
    defaultCategory?: string;
    defaultModel?: string;
    defaultTemperature?: number;
    defaultMaxTokens?: number;
  };
}

// 文件上传类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error' | 'removed';
  url?: string;
  thumbUrl?: string;
  size?: number;
  type?: string;
}

// 导出类型
export interface ExportOptions {
  format: 'pdf' | 'word' | 'markdown' | 'html' | 'txt';
  includeImages: boolean;
  includeMetadata: boolean;
  template?: string;
}

// 历史记录类型
export interface HistoryItem extends GenerationResponse {
  isFavorite?: boolean;
  tags?: string[];
  notes?: string;
}

export interface HistoryFilter {
  status?: GenerationResponse['status'][];
  category?: string[];
  dateRange?: [string, string];
  isFavorite?: boolean;
  hasImages?: boolean;
}

// 统计数据类型
export interface DashboardStats extends GenerationStats {
  todayCount: number;
  weekCount: number;
  monthCount: number;
  averageDuration: number;
  totalTokens: number;
  favoriteCount: number;
}

// 错误类型
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface FormErrors {
  [key: string]: string | ValidationError[];
}
