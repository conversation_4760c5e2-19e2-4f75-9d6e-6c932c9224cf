import apiClient from './api';
import {
  GenerationRequest,
  GenerationResponse,
  GenerationListResponse,
  GenerationStats,
  SearchParams,
} from '@/types';

export class GenerationService {
  /**
   * 创建新的生成任务
   */
  static async createGeneration(request: GenerationRequest): Promise<GenerationResponse> {
    return apiClient.post('/generation', request);
  }

  /**
   * 获取生成任务详情
   */
  static async getGeneration(id: string): Promise<GenerationResponse> {
    return apiClient.get(`/generation/${id}`);
  }

  /**
   * 获取用户的生成历史
   */
  static async getUserGenerations(params: {
    page?: number;
    limit?: number;
  } = {}): Promise<GenerationListResponse> {
    const { page = 1, limit = 20 } = params;
    return apiClient.get('/generation', {
      params: { page, limit },
    });
  }

  /**
   * 搜索生成历史
   */
  static async searchGenerations(params: SearchParams): Promise<GenerationListResponse> {
    const { query, page = 1, limit = 20, ...filters } = params;
    return apiClient.get('/generation/search', {
      params: {
        q: query,
        page,
        limit,
        ...filters,
      },
    });
  }

  /**
   * 删除生成任务
   */
  static async deleteGeneration(id: string): Promise<void> {
    return apiClient.delete(`/generation/${id}`);
  }

  /**
   * 获取生成统计信息
   */
  static async getGenerationStats(): Promise<GenerationStats> {
    return apiClient.get('/generation/stats');
  }

  /**
   * 批量删除生成任务
   */
  static async batchDeleteGenerations(ids: string[]): Promise<void> {
    return apiClient.post('/generation/batch-delete', { ids });
  }

  /**
   * 导出生成结果
   */
  static async exportGeneration(
    id: string,
    format: 'pdf' | 'word' | 'markdown' | 'html' | 'txt'
  ): Promise<void> {
    return apiClient.download(`/generation/${id}/export`, `generation-${id}.${format}`, {
      params: { format },
    });
  }

  /**
   * 批量导出生成结果
   */
  static async batchExportGenerations(
    ids: string[],
    format: 'pdf' | 'word' | 'markdown' | 'html' | 'txt'
  ): Promise<void> {
    return apiClient.download('/generation/batch-export', `generations.${format}`, {
      params: { ids: ids.join(','), format },
    });
  }

  /**
   * 收藏/取消收藏生成结果
   */
  static async toggleFavorite(id: string, isFavorite: boolean): Promise<void> {
    return apiClient.patch(`/generation/${id}/favorite`, { isFavorite });
  }

  /**
   * 添加标签
   */
  static async addTags(id: string, tags: string[]): Promise<void> {
    return apiClient.patch(`/generation/${id}/tags`, { tags });
  }

  /**
   * 添加备注
   */
  static async addNotes(id: string, notes: string): Promise<void> {
    return apiClient.patch(`/generation/${id}/notes`, { notes });
  }

  /**
   * 重新生成
   */
  static async regenerate(id: string): Promise<GenerationResponse> {
    return apiClient.post(`/generation/${id}/regenerate`);
  }

  /**
   * 获取生成进度
   */
  static async getGenerationProgress(id: string): Promise<{
    progress: number;
    status: string;
    message?: string;
  }> {
    return apiClient.get(`/generation/${id}/progress`);
  }

  /**
   * 取消生成任务
   */
  static async cancelGeneration(id: string): Promise<void> {
    return apiClient.post(`/generation/${id}/cancel`);
  }

  /**
   * 获取生成历史统计图表数据
   */
  static async getGenerationChartData(params: {
    period: 'week' | 'month' | 'year';
    type: 'count' | 'duration' | 'tokens';
  }): Promise<{
    labels: string[];
    data: number[];
  }> {
    return apiClient.get('/generation/chart-data', { params });
  }

  /**
   * 获取热门分类
   */
  static async getPopularCategories(): Promise<{
    category: string;
    count: number;
  }[]> {
    return apiClient.get('/generation/popular-categories');
  }

  /**
   * 获取推荐提示词
   */
  static async getRecommendedPrompts(category?: string): Promise<{
    prompt: string;
    category: string;
    description?: string;
  }[]> {
    return apiClient.get('/generation/recommended-prompts', {
      params: { category },
    });
  }

  /**
   * 分享生成结果
   */
  static async shareGeneration(id: string, options: {
    platform: 'link' | 'email' | 'social';
    recipients?: string[];
    message?: string;
  }): Promise<{
    shareUrl?: string;
    shareId?: string;
  }> {
    return apiClient.post(`/generation/${id}/share`, options);
  }

  /**
   * 获取分享的生成结果
   */
  static async getSharedGeneration(shareId: string): Promise<GenerationResponse> {
    return apiClient.get(`/generation/shared/${shareId}`);
  }

  /**
   * 复制生成结果
   */
  static async duplicateGeneration(id: string): Promise<GenerationResponse> {
    return apiClient.post(`/generation/${id}/duplicate`);
  }

  /**
   * 获取生成模板
   */
  static async getGenerationTemplates(): Promise<{
    id: string;
    name: string;
    description: string;
    category: string;
    prompt: string;
    options?: any;
  }[]> {
    return apiClient.get('/generation/templates');
  }

  /**
   * 保存为模板
   */
  static async saveAsTemplate(id: string, template: {
    name: string;
    description: string;
    category: string;
  }): Promise<void> {
    return apiClient.post(`/generation/${id}/save-template`, template);
  }
}

export default GenerationService;
