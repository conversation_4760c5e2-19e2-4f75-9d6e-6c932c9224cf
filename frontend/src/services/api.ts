import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import { ApiResponse, ApiError } from '@/types';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加请求ID用于追踪
    const requestId = Math.random().toString(36).substring(7);
    if (config.headers) {
      config.headers['X-Request-ID'] = requestId;
    }

    console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`, {
      requestId,
      data: config.data,
      params: config.params,
    });

    return config;
  },
  (error) => {
    console.error('[API Request Error]', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const requestId = response.config.headers?.['X-Request-ID'];
    console.log(`[API Response] ${response.status}`, {
      requestId,
      data: response.data,
    });

    // 检查业务状态码
    if (response.data && !response.data.success) {
      const error: ApiError = {
        message: response.data.message || '请求失败',
        code: response.data.error,
        statusCode: response.status,
      };
      return Promise.reject(error);
    }

    return response;
  },
  (error) => {
    const requestId = error.config?.headers?.['X-Request-ID'];
    console.error(`[API Response Error]`, {
      requestId,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });

    // 处理不同类型的错误
    let apiError: ApiError;

    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          window.location.href = '/login';
          apiError = {
            message: '登录已过期，请重新登录',
            code: 'UNAUTHORIZED',
            statusCode: status,
          };
          break;
        case 403:
          apiError = {
            message: '没有权限访问此资源',
            code: 'FORBIDDEN',
            statusCode: status,
          };
          break;
        case 404:
          apiError = {
            message: '请求的资源不存在',
            code: 'NOT_FOUND',
            statusCode: status,
          };
          break;
        case 429:
          apiError = {
            message: '请求过于频繁，请稍后再试',
            code: 'RATE_LIMIT',
            statusCode: status,
          };
          break;
        case 500:
          apiError = {
            message: '服务器内部错误',
            code: 'INTERNAL_ERROR',
            statusCode: status,
          };
          break;
        default:
          apiError = {
            message: data?.message || `请求失败 (${status})`,
            code: data?.error || 'REQUEST_FAILED',
            statusCode: status,
            details: data,
          };
      }
    } else if (error.request) {
      // 网络错误
      apiError = {
        message: '网络连接失败，请检查网络设置',
        code: 'NETWORK_ERROR',
      };
    } else {
      // 其他错误
      apiError = {
        message: error.message || '未知错误',
        code: 'UNKNOWN_ERROR',
      };
    }

    // 显示错误消息（除了401错误）
    if (apiError.statusCode !== 401) {
      message.error(apiError.message);
    }

    return Promise.reject(apiError);
  }
);

// API方法封装
export const apiClient = {
  // GET请求
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return api.get(url, config).then(response => response.data.data);
  },

  // POST请求
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return api.post(url, data, config).then(response => response.data.data);
  },

  // PUT请求
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return api.put(url, data, config).then(response => response.data.data);
  },

  // PATCH请求
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return api.patch(url, data, config).then(response => response.data.data);
  },

  // DELETE请求
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return api.delete(url, config).then(response => response.data.data);
  },

  // 上传文件
  upload: <T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> => {
    return api.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    }).then(response => response.data.data);
  },

  // 下载文件
  download: (url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> => {
    return api.get(url, {
      ...config,
      responseType: 'blob',
    }).then(response => {
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    });
  },
};

// 导出原始axios实例（用于特殊需求）
export { api };
export default apiClient;
