import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Tag,
  Tooltip,
  Modal,
  message,
  Dropdown,
  Typography,
  Row,
  Col,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  DeleteOutlined,
  DownloadOutlined,
  StarOutlined,
  StarFilled,
  MoreOutlined,
  ReloadOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import GenerationService from '@/services/generationService';
import { GenerationResponse, SearchParams } from '@/types';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text } = Typography;

const History: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [searchParams, setSearchParams] = useState<SearchParams>({
    page: 1,
    limit: 20,
    query: '',
    status: undefined,
    category: undefined,
  });

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 获取历史记录
  const { data: historyData, isLoading, refetch } = useQuery({
    queryKey: ['generation-history', searchParams],
    queryFn: () => {
      if (searchParams.query) {
        return GenerationService.searchGenerations(searchParams);
      }
      return GenerationService.getUserGenerations({
        page: searchParams.page,
        limit: searchParams.limit,
      });
    },
  });

  // 删除生成记录
  const deleteMutation = useMutation({
    mutationFn: GenerationService.deleteGeneration,
    onSuccess: () => {
      message.success('删除成功');
      queryClient.invalidateQueries({ queryKey: ['generation-history'] });
      queryClient.invalidateQueries({ queryKey: ['generation-stats'] });
    },
    onError: (error: any) => {
      message.error(error.message || '删除失败');
    },
  });

  // 批量删除
  const batchDeleteMutation = useMutation({
    mutationFn: GenerationService.batchDeleteGenerations,
    onSuccess: () => {
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      queryClient.invalidateQueries({ queryKey: ['generation-history'] });
      queryClient.invalidateQueries({ queryKey: ['generation-stats'] });
    },
    onError: (error: any) => {
      message.error(error.message || '批量删除失败');
    },
  });

  // 收藏/取消收藏
  const toggleFavoriteMutation = useMutation({
    mutationFn: ({ id, isFavorite }: { id: string; isFavorite: boolean }) =>
      GenerationService.toggleFavorite(id, isFavorite),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generation-history'] });
    },
    onError: (error: any) => {
      message.error(error.message || '操作失败');
    },
  });

  const handleSearch = (value: string) => {
    setSearchParams(prev => ({
      ...prev,
      query: value,
      page: 1,
    }));
  };

  const handleFilterChange = (key: string, value: any) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  const handleView = (record: GenerationResponse) => {
    navigate(`/generation/${record.id}`);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条生成记录吗？此操作不可恢复。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => deleteMutation.mutate(id),
    });
  };

  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => batchDeleteMutation.mutate(selectedRowKeys),
    });
  };

  const handleToggleFavorite = (record: GenerationResponse) => {
    toggleFavoriteMutation.mutate({
      id: record.id,
      isFavorite: !record.isFavorite,
    });
  };

  const handleCopyContent = (content: string) => {
    navigator.clipboard.writeText(content);
    message.success('内容已复制到剪贴板');
  };

  const handleExport = (id: string, format: 'pdf' | 'markdown' = 'markdown') => {
    GenerationService.exportGeneration(id, format);
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'default', text: '等待中' },
      processing: { color: 'processing', text: '处理中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: '内容预览',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content: string, record: GenerationResponse) => (
        <div>
          <Text ellipsis={{ tooltip: content }} style={{ maxWidth: 300 }}>
            {content || record.prompt || '生成中...'}
          </Text>
          {record.isFavorite && (
            <StarFilled style={{ color: '#fadb14', marginLeft: 8 }} />
          )}
        </div>
      ),
    },
    {
      title: '类型',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => category ? <Tag>{category}</Tag> : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record: GenerationResponse) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          
          <Tooltip title={record.isFavorite ? '取消收藏' : '收藏'}>
            <Button
              type="text"
              icon={record.isFavorite ? <StarFilled /> : <StarOutlined />}
              onClick={() => handleToggleFavorite(record)}
              style={{ color: record.isFavorite ? '#fadb14' : undefined }}
            />
          </Tooltip>

          <Dropdown
            menu={{
              items: [
                {
                  key: 'copy',
                  icon: <CopyOutlined />,
                  label: '复制内容',
                  onClick: () => handleCopyContent(record.content),
                  disabled: !record.content,
                },
                {
                  key: 'download-md',
                  icon: <DownloadOutlined />,
                  label: '导出 Markdown',
                  onClick: () => handleExport(record.id, 'markdown'),
                  disabled: !record.content,
                },
                {
                  key: 'download-pdf',
                  icon: <DownloadOutlined />,
                  label: '导出 PDF',
                  onClick: () => handleExport(record.id, 'pdf'),
                  disabled: !record.content,
                },
                {
                  type: 'divider',
                },
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: '删除',
                  danger: true,
                  onClick: () => handleDelete(record.id),
                },
              ],
            }}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
  };

  return (
    <div>
      <Card>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Input.Search
              placeholder="搜索内容..."
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          
          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="completed">已完成</Option>
              <Option value="processing">处理中</Option>
              <Option value="pending">等待中</Option>
              <Option value="failed">失败</Option>
            </Select>
          </Col>

          <Col xs={24} sm={12} md={4}>
            <Select
              placeholder="类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilterChange('category', value)}
            >
              <Option value="article">文章</Option>
              <Option value="story">故事</Option>
              <Option value="code">代码</Option>
              <Option value="email">邮件</Option>
              <Option value="summary">摘要</Option>
              <Option value="translation">翻译</Option>
              <Option value="other">其他</Option>
            </Select>
          </Col>

          <Col xs={24} sm={12} md={8}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => {
                if (dates) {
                  handleFilterChange('startDate', dates[0]?.toISOString());
                  handleFilterChange('endDate', dates[1]?.toISOString());
                } else {
                  handleFilterChange('startDate', undefined);
                  handleFilterChange('endDate', undefined);
                }
              }}
            />
          </Col>
        </Row>

        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              {selectedRowKeys.length > 0 && (
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                  loading={batchDeleteMutation.isPending}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              )}
            </Space>
          </Col>
          
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => refetch()}
              loading={isLoading}
            >
              刷新
            </Button>
          </Col>
        </Row>

        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={historyData?.generations || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.limit,
            total: historyData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setSearchParams(prev => ({
                ...prev,
                page,
                limit: pageSize || 20,
              }));
            },
          }}
        />
      </Card>
    </div>
  );
};

export default History;
