import React from 'react';
import { Card, Row, Col, Stati<PERSON>, Button, Typography, Space, Divider } from 'antd';
import {
  PlusOutlined,
  HistoryOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import GenerationService from '@/services/generationService';
import { GenerationStats } from '@/types';

const { Title, Paragraph } = Typography;

const Home: React.FC = () => {
  const navigate = useNavigate();

  // 获取统计数据
  const { data: stats, isLoading: statsLoading } = useQuery<GenerationStats>({
    queryKey: ['generation-stats'],
    queryFn: GenerationService.getGenerationStats,
  });

  // 获取最近的生成记录
  const { data: recentGenerations, isLoading: recentLoading } = useQuery({
    queryKey: ['recent-generations'],
    queryFn: () => GenerationService.getUserGenerations({ page: 1, limit: 5 }),
  });

  const handleStartGeneration = () => {
    navigate('/generation');
  };

  const handleViewHistory = () => {
    navigate('/history');
  };

  return (
    <div>
      {/* 欢迎区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Title level={2} style={{ margin: 0 }}>
              欢迎使用 Coze Clone Platform
            </Title>
            <Paragraph style={{ margin: '8px 0 0 0', fontSize: 16 }}>
              基于 MCP Chrome 的智能内容生成平台，让 AI 创作更简单
            </Paragraph>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                size="large"
                icon={<PlusOutlined />}
                onClick={handleStartGeneration}
              >
                开始生成
              </Button>
              <Button
                size="large"
                icon={<HistoryOutlined />}
                onClick={handleViewHistory}
              >
                查看历史
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 统计数据 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总生成数"
              value={stats?.total || 0}
              prefix={<FileTextOutlined />}
              loading={statsLoading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats?.completed || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
              loading={statsLoading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="处理中"
              value={stats?.processing || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
              loading={statsLoading}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="失败"
              value={stats?.failed || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
              loading={statsLoading}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 快速开始 */}
        <Col xs={24} lg={12}>
          <Card title="快速开始" style={{ height: '100%' }}>
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Title level={4}>1. 输入您的需求</Title>
                <Paragraph>
                  在生成页面输入您想要创建的内容描述，支持各种类型的创作需求。
                </Paragraph>
              </div>
              
              <div>
                <Title level={4}>2. 选择生成选项</Title>
                <Paragraph>
                  根据需要调整生成参数，如内容类型、创意程度等。
                </Paragraph>
              </div>
              
              <div>
                <Title level={4}>3. 获取结果</Title>
                <Paragraph>
                  系统将自动处理您的请求，并实时显示生成进度和结果。
                </Paragraph>
              </div>
              
              <Button
                type="primary"
                size="large"
                icon={<PlusOutlined />}
                onClick={handleStartGeneration}
                block
              >
                立即开始生成
              </Button>
            </Space>
          </Card>
        </Col>

        {/* 最近生成 */}
        <Col xs={24} lg={12}>
          <Card
            title="最近生成"
            extra={
              <Button type="link" onClick={handleViewHistory}>
                查看全部
              </Button>
            }
            style={{ height: '100%' }}
          >
            {recentLoading ? (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                加载中...
              </div>
            ) : recentGenerations?.generations?.length ? (
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {recentGenerations.generations.map((generation, index) => (
                  <div key={generation.id}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div style={{ flex: 1 }}>
                        <Paragraph
                          ellipsis={{ rows: 2 }}
                          style={{ margin: 0, fontWeight: 500 }}
                        >
                          {generation.content || '生成中...'}
                        </Paragraph>
                        <Paragraph
                          type="secondary"
                          style={{ margin: '4px 0 0 0', fontSize: 12 }}
                        >
                          {new Date(generation.createdAt).toLocaleString()}
                        </Paragraph>
                      </div>
                      <div style={{ marginLeft: 12 }}>
                        <Button
                          type="link"
                          size="small"
                          onClick={() => navigate(`/generation/${generation.id}`)}
                        >
                          查看
                        </Button>
                      </div>
                    </div>
                    {index < recentGenerations.generations.length - 1 && (
                      <Divider style={{ margin: '12px 0' }} />
                    )}
                  </div>
                ))}
              </Space>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Paragraph type="secondary">
                  还没有生成记录，
                  <Button type="link" onClick={handleStartGeneration} style={{ padding: 0 }}>
                    立即开始
                  </Button>
                </Paragraph>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Home;
