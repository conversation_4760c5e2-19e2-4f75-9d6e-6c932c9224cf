import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Slider,
  Space,
  Typography,
  Progress,
  Alert,
  Divider,
  Row,
  Col,
  message,
} from 'antd';
import {
  SendOutlined,
  ReloadOutlined,
  SaveOutlined,
  CopyOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

import GenerationService from '@/services/generationService';
import { useSocket } from '@/hooks/useSocket';
import { GenerationRequest, GenerationResponse } from '@/types';

const { TextArea } = Input;
const { Title, Paragraph } = Typography;
const { Option } = Select;

const Generation: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { on, off } = useSocket();

  const [currentGeneration, setCurrentGeneration] = useState<GenerationResponse | null>(null);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);

  // 如果有ID，获取现有的生成记录
  const { data: existingGeneration, isLoading } = useQuery({
    queryKey: ['generation', id],
    queryFn: () => GenerationService.getGeneration(id!),
    enabled: !!id,
  });

  // 创建生成任务
  const createGenerationMutation = useMutation({
    mutationFn: GenerationService.createGeneration,
    onSuccess: (data) => {
      setCurrentGeneration(data);
      setIsGenerating(true);
      setGenerationProgress(0);
      message.success('生成任务已创建');
    },
    onError: (error: any) => {
      message.error(error.message || '创建生成任务失败');
    },
  });

  // Socket事件监听
  useEffect(() => {
    const handleGenerationStatus = (data: any) => {
      if (data.id === currentGeneration?.id) {
        setCurrentGeneration(prev => prev ? { ...prev, status: data.status } : null);
        
        if (data.status === 'processing') {
          setIsGenerating(true);
        } else if (data.status === 'completed' || data.status === 'failed') {
          setIsGenerating(false);
          setGenerationProgress(100);
        }
      }
    };

    const handleGenerationCompleted = (data: any) => {
      if (data.id === currentGeneration?.id) {
        setCurrentGeneration(prev => prev ? {
          ...prev,
          content: data.content,
          images: data.images,
          metadata: data.metadata,
          status: 'completed',
          completedAt: new Date().toISOString(),
        } : null);
        setIsGenerating(false);
        setGenerationProgress(100);
      }
    };

    const handleGenerationFailed = (data: any) => {
      if (data.id === currentGeneration?.id) {
        setCurrentGeneration(prev => prev ? {
          ...prev,
          status: 'failed',
          error: data.error,
          completedAt: new Date().toISOString(),
        } : null);
        setIsGenerating(false);
        setGenerationProgress(0);
      }
    };

    const handleGenerationProgress = (data: any) => {
      if (data.id === currentGeneration?.id) {
        setGenerationProgress(data.progress);
      }
    };

    on('generation:status', handleGenerationStatus);
    on('generation:completed', handleGenerationCompleted);
    on('generation:failed', handleGenerationFailed);
    on('generation:progress', handleGenerationProgress);

    return () => {
      off('generation:status', handleGenerationStatus);
      off('generation:completed', handleGenerationCompleted);
      off('generation:failed', handleGenerationFailed);
      off('generation:progress', handleGenerationProgress);
    };
  }, [currentGeneration?.id, on, off]);

  // 设置现有生成记录
  useEffect(() => {
    if (existingGeneration) {
      setCurrentGeneration(existingGeneration);
      form.setFieldsValue({
        prompt: existingGeneration.metadata?.originalPrompt || '',
        category: existingGeneration.metadata?.category,
        temperature: existingGeneration.metadata?.temperature || 0.7,
        maxTokens: existingGeneration.metadata?.maxTokens || 2000,
      });
    }
  }, [existingGeneration, form]);

  const handleSubmit = async (values: any) => {
    const request: GenerationRequest = {
      prompt: values.prompt,
      category: values.category,
      options: {
        temperature: values.temperature,
        maxTokens: values.maxTokens,
        model: values.model,
        saveToHistory: true,
      },
    };

    createGenerationMutation.mutate(request);
  };

  const handleCopyContent = () => {
    if (currentGeneration?.content) {
      navigator.clipboard.writeText(currentGeneration.content);
      message.success('内容已复制到剪贴板');
    }
  };

  const handleDownload = () => {
    if (currentGeneration?.id) {
      GenerationService.exportGeneration(currentGeneration.id, 'markdown');
    }
  };

  const handleRegenerate = () => {
    if (currentGeneration?.id) {
      GenerationService.regenerate(currentGeneration.id).then((newGeneration) => {
        setCurrentGeneration(newGeneration);
        setIsGenerating(true);
        setGenerationProgress(0);
      });
    }
  };

  if (isLoading) {
    return <div>加载中...</div>;
  }

  return (
    <Row gutter={[24, 24]}>
      {/* 左侧：输入区域 */}
      <Col xs={24} lg={12}>
        <Card title="内容生成" style={{ height: 'fit-content' }}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              temperature: 0.7,
              maxTokens: 2000,
              model: 'default',
            }}
          >
            <Form.Item
              name="prompt"
              label="输入您的需求"
              rules={[{ required: true, message: '请输入生成需求' }]}
            >
              <TextArea
                rows={6}
                placeholder="请详细描述您想要生成的内容..."
                showCount
                maxLength={10000}
              />
            </Form.Item>

            <Form.Item name="category" label="内容类型">
              <Select placeholder="选择内容类型（可选）">
                <Option value="article">文章</Option>
                <Option value="story">故事</Option>
                <Option value="code">代码</Option>
                <Option value="email">邮件</Option>
                <Option value="summary">摘要</Option>
                <Option value="translation">翻译</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="temperature" label="创意程度">
                  <Slider
                    min={0}
                    max={2}
                    step={0.1}
                    marks={{
                      0: '保守',
                      1: '平衡',
                      2: '创新',
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="maxTokens" label="最大长度">
                  <Slider
                    min={100}
                    max={4000}
                    step={100}
                    marks={{
                      100: '短',
                      2000: '中',
                      4000: '长',
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SendOutlined />}
                  loading={createGenerationMutation.isPending || isGenerating}
                  disabled={isGenerating}
                >
                  {isGenerating ? '生成中...' : '开始生成'}
                </Button>
                {currentGeneration && (
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleRegenerate}
                    disabled={isGenerating}
                  >
                    重新生成
                  </Button>
                )}
              </Space>
            </Form.Item>
          </Form>
        </Card>
      </Col>

      {/* 右侧：结果区域 */}
      <Col xs={24} lg={12}>
        <Card
          title="生成结果"
          extra={
            currentGeneration?.content && (
              <Space>
                <Button
                  type="text"
                  icon={<CopyOutlined />}
                  onClick={handleCopyContent}
                >
                  复制
                </Button>
                <Button
                  type="text"
                  icon={<DownloadOutlined />}
                  onClick={handleDownload}
                >
                  下载
                </Button>
              </Space>
            )
          }
          style={{ height: 'fit-content', minHeight: 400 }}
        >
          {isGenerating && (
            <div style={{ marginBottom: 16 }}>
              <Progress
                percent={generationProgress}
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
              <Paragraph type="secondary" style={{ marginTop: 8 }}>
                正在生成内容，请稍候...
              </Paragraph>
            </div>
          )}

          {currentGeneration?.status === 'failed' && (
            <Alert
              message="生成失败"
              description={currentGeneration.error}
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          {currentGeneration?.content ? (
            <div>
              <div style={{ marginBottom: 16 }}>
                <ReactMarkdown
                  components={{
                    code({ node, inline, className, children, ...props }) {
                      const match = /language-(\w+)/.exec(className || '');
                      return !inline && match ? (
                        <SyntaxHighlighter
                          style={tomorrow}
                          language={match[1]}
                          PreTag="div"
                          {...props}
                        >
                          {String(children).replace(/\n$/, '')}
                        </SyntaxHighlighter>
                      ) : (
                        <code className={className} {...props}>
                          {children}
                        </code>
                      );
                    },
                  }}
                >
                  {currentGeneration.content}
                </ReactMarkdown>
              </div>

              {currentGeneration.images && currentGeneration.images.length > 0 && (
                <div>
                  <Divider>生成的图片</Divider>
                  <Row gutter={[8, 8]}>
                    {currentGeneration.images.map((image, index) => (
                      <Col key={index} span={12}>
                        <img
                          src={image}
                          alt={`Generated ${index + 1}`}
                          style={{ width: '100%', borderRadius: 4 }}
                        />
                      </Col>
                    ))}
                  </Row>
                </div>
              )}

              {currentGeneration.metadata && (
                <div style={{ marginTop: 16 }}>
                  <Divider>生成信息</Divider>
                  <Paragraph type="secondary" style={{ fontSize: 12 }}>
                    生成时间: {new Date(currentGeneration.createdAt).toLocaleString()}
                    {currentGeneration.metadata.duration && (
                      <> | 耗时: {currentGeneration.metadata.duration}ms</>
                    )}
                    {currentGeneration.metadata.tokens && (
                      <> | Token数: {currentGeneration.metadata.tokens}</>
                    )}
                  </Paragraph>
                </div>
              )}
            </div>
          ) : !isGenerating && !currentGeneration ? (
            <div style={{ textAlign: 'center', padding: '60px 0', color: '#999' }}>
              <Paragraph>请在左侧输入您的需求，然后点击"开始生成"</Paragraph>
            </div>
          ) : null}
        </Card>
      </Col>
    </Row>
  );
};

export default Generation;
