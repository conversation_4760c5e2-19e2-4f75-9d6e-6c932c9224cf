import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  Switch,
  Select,
  Slider,
  Divider,
  Row,
  Col,
  Typography,
  Space,
  message,
  Modal,
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  LockOutlined,
  SettingOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import apiClient from '@/services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface UserSettings {
  theme: string;
  language: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  soundNotifications: boolean;
  defaultCategory?: string;
  defaultModel?: string;
  defaultTemperature?: number;
  defaultMaxTokens?: number;
  autoSave: boolean;
}

const Profile: React.FC = () => {
  const { user, updateUser } = useAuth();
  const queryClient = useQueryClient();
  const [profileForm] = Form.useForm();
  const [settingsForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('profile');

  // 获取用户设置
  const { data: settings, isLoading: settingsLoading } = useQuery<UserSettings>({
    queryKey: ['user-settings'],
    queryFn: () => apiClient.get('/user/settings'),
  });

  // 更新用户资料
  const updateProfileMutation = useMutation({
    mutationFn: (data: any) => updateUser(data),
    onSuccess: () => {
      message.success('资料更新成功');
    },
    onError: (error: any) => {
      message.error(error.message || '更新失败');
    },
  });

  // 更新用户设置
  const updateSettingsMutation = useMutation({
    mutationFn: (data: UserSettings) => apiClient.put('/user/settings', data),
    onSuccess: () => {
      message.success('设置更新成功');
      queryClient.invalidateQueries({ queryKey: ['user-settings'] });
    },
    onError: (error: any) => {
      message.error(error.message || '更新失败');
    },
  });

  // 修改密码
  const changePasswordMutation = useMutation({
    mutationFn: (data: any) => apiClient.post('/auth/change-password', data),
    onSuccess: () => {
      message.success('密码修改成功');
      passwordForm.resetFields();
    },
    onError: (error: any) => {
      message.error(error.message || '修改失败');
    },
  });

  const handleProfileSubmit = (values: any) => {
    updateProfileMutation.mutate(values);
  };

  const handleSettingsSubmit = (values: UserSettings) => {
    updateSettingsMutation.mutate(values);
  };

  const handlePasswordSubmit = (values: any) => {
    changePasswordMutation.mutate(values);
  };

  const handleAvatarChange = (info: any) => {
    if (info.file.status === 'done') {
      const avatarUrl = info.file.response?.data?.url;
      if (avatarUrl) {
        updateUser({ avatar: avatarUrl });
      }
    }
  };

  const uploadButton = (
    <div>
      <CameraOutlined />
      <div style={{ marginTop: 8 }}>上传头像</div>
    </div>
  );

  const tabItems = [
    {
      key: 'profile',
      label: '个人资料',
      icon: <UserOutlined />,
    },
    {
      key: 'settings',
      label: '偏好设置',
      icon: <SettingOutlined />,
    },
    {
      key: 'password',
      label: '修改密码',
      icon: <LockOutlined />,
    },
  ];

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* 左侧导航 */}
        <Col xs={24} md={6}>
          <Card>
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Avatar
                size={80}
                src={user?.avatar}
                icon={<UserOutlined />}
                style={{ marginBottom: 16 }}
              />
              <Title level={4} style={{ margin: 0 }}>
                {user?.username}
              </Title>
              <Text type="secondary">{user?.email}</Text>
            </div>

            <div>
              {tabItems.map(item => (
                <div
                  key={item.key}
                  style={{
                    padding: '12px 16px',
                    cursor: 'pointer',
                    borderRadius: 6,
                    marginBottom: 4,
                    backgroundColor: activeTab === item.key ? '#f0f2ff' : 'transparent',
                    color: activeTab === item.key ? '#1890ff' : undefined,
                  }}
                  onClick={() => setActiveTab(item.key)}
                >
                  <Space>
                    {item.icon}
                    {item.label}
                  </Space>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        {/* 右侧内容 */}
        <Col xs={24} md={18}>
          {activeTab === 'profile' && (
            <Card title="个人资料">
              <Form
                form={profileForm}
                layout="vertical"
                onFinish={handleProfileSubmit}
                initialValues={{
                  username: user?.username,
                  email: user?.email,
                }}
              >
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="头像">
                      <Upload
                        name="avatar"
                        listType="picture-card"
                        className="avatar-uploader"
                        showUploadList={false}
                        action="/api/upload/avatar"
                        beforeUpload={(file) => {
                          const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
                          if (!isJpgOrPng) {
                            message.error('只能上传 JPG/PNG 格式的图片');
                          }
                          const isLt2M = file.size / 1024 / 1024 < 2;
                          if (!isLt2M) {
                            message.error('图片大小不能超过 2MB');
                          }
                          return isJpgOrPng && isLt2M;
                        }}
                        onChange={handleAvatarChange}
                      >
                        {user?.avatar ? (
                          <img src={user.avatar} alt="avatar" style={{ width: '100%' }} />
                        ) : (
                          uploadButton
                        )}
                      </Upload>
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item
                      name="username"
                      label="用户名"
                      rules={[
                        { required: true, message: '请输入用户名' },
                        { min: 3, message: '用户名至少3个字符' },
                        { max: 30, message: '用户名最多30个字符' },
                      ]}
                    >
                      <Input placeholder="请输入用户名" />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item
                      name="email"
                      label="邮箱"
                      rules={[
                        { required: true, message: '请输入邮箱' },
                        { type: 'email', message: '请输入有效的邮箱地址' },
                      ]}
                    >
                      <Input placeholder="请输入邮箱" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={updateProfileMutation.isPending}
                  >
                    保存更改
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          )}

          {activeTab === 'settings' && (
            <Card title="偏好设置">
              <Form
                form={settingsForm}
                layout="vertical"
                onFinish={handleSettingsSubmit}
                initialValues={settings}
              >
                <Title level={5}>界面设置</Title>
                <Row gutter={24}>
                  <Col xs={24} md={12}>
                    <Form.Item name="theme" label="主题">
                      <Select>
                        <Option value="light">浅色</Option>
                        <Option value="dark">深色</Option>
                        <Option value="auto">跟随系统</Option>
                      </Select>
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item name="language" label="语言">
                      <Select>
                        <Option value="zh-CN">中文</Option>
                        <Option value="en-US">English</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />

                <Title level={5}>通知设置</Title>
                <Row gutter={24}>
                  <Col xs={24} md={8}>
                    <Form.Item name="emailNotifications" label="邮件通知" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={8}>
                    <Form.Item name="pushNotifications" label="推送通知" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={8}>
                    <Form.Item name="soundNotifications" label="声音提醒" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>

                <Divider />

                <Title level={5}>生成设置</Title>
                <Row gutter={24}>
                  <Col xs={24} md={12}>
                    <Form.Item name="defaultCategory" label="默认类型">
                      <Select placeholder="选择默认内容类型">
                        <Option value="article">文章</Option>
                        <Option value="story">故事</Option>
                        <Option value="code">代码</Option>
                        <Option value="email">邮件</Option>
                        <Option value="summary">摘要</Option>
                        <Option value="translation">翻译</Option>
                        <Option value="other">其他</Option>
                      </Select>
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item name="autoSave" label="自动保存" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item name="defaultTemperature" label="默认创意程度">
                      <Slider
                        min={0}
                        max={2}
                        step={0.1}
                        marks={{
                          0: '保守',
                          1: '平衡',
                          2: '创新',
                        }}
                      />
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <Form.Item name="defaultMaxTokens" label="默认最大长度">
                      <Slider
                        min={100}
                        max={4000}
                        step={100}
                        marks={{
                          100: '短',
                          2000: '中',
                          4000: '长',
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={updateSettingsMutation.isPending}
                  >
                    保存设置
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          )}

          {activeTab === 'password' && (
            <Card title="修改密码">
              <Form
                form={passwordForm}
                layout="vertical"
                onFinish={handlePasswordSubmit}
              >
                <Row gutter={24}>
                  <Col xs={24} md={12}>
                    <Form.Item
                      name="currentPassword"
                      label="当前密码"
                      rules={[{ required: true, message: '请输入当前密码' }]}
                    >
                      <Input.Password placeholder="请输入当前密码" />
                    </Form.Item>

                    <Form.Item
                      name="newPassword"
                      label="新密码"
                      rules={[
                        { required: true, message: '请输入新密码' },
                        { min: 6, message: '密码至少6个字符' },
                        {
                          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                          message: '密码必须包含至少一个小写字母、一个大写字母和一个数字',
                        },
                      ]}
                    >
                      <Input.Password placeholder="请输入新密码" />
                    </Form.Item>

                    <Form.Item
                      name="confirmNewPassword"
                      label="确认新密码"
                      dependencies={['newPassword']}
                      rules={[
                        { required: true, message: '请确认新密码' },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (!value || getFieldValue('newPassword') === value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(new Error('两次输入的密码不一致'));
                          },
                        }),
                      ]}
                    >
                      <Input.Password placeholder="请再次输入新密码" />
                    </Form.Item>

                    <Form.Item>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={changePasswordMutation.isPending}
                      >
                        修改密码
                      </Button>
                    </Form.Item>
                  </Col>

                  <Col xs={24} md={12}>
                    <div style={{ padding: 16, backgroundColor: '#f6f8fa', borderRadius: 6 }}>
                      <Title level={5}>密码安全建议</Title>
                      <ul style={{ margin: 0, paddingLeft: 20 }}>
                        <li>密码长度至少6个字符</li>
                        <li>包含大小写字母和数字</li>
                        <li>不要使用常见的密码</li>
                        <li>定期更换密码</li>
                        <li>不要在多个网站使用相同密码</li>
                      </ul>
                    </div>
                  </Col>
                </Row>
              </Form>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default Profile;
