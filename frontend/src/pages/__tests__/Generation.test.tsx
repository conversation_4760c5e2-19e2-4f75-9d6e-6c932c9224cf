import { render, screen, waitFor } from '@/test/utils';
import { mockUserInteraction, mockGeneration } from '@/test/utils';
import Generation from '@/pages/Generation';
import { server } from '@/test/mocks/server';
import { rest } from 'msw';

// Mock useParams
const mockParams = { id: undefined };
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => mockParams,
}));

// Mock socket events
const mockSocketEvents = {
  on: jest.fn(),
  off: jest.fn(),
};

jest.mock('@/hooks/useSocket', () => ({
  useSocket: () => mockSocketEvents,
}));

describe('Generation Page', () => {
  beforeEach(() => {
    mockParams.id = undefined;
    mockSocketEvents.on.mockClear();
    mockSocketEvents.off.mockClear();
  });

  describe('新建生成', () => {
    it('should render generation form', () => {
      render(<Generation />);

      expect(screen.getByText('内容生成')).toBeInTheDocument();
      expect(screen.getByText('生成结果')).toBeInTheDocument();
      expect(screen.getByLabelText('输入您的需求')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '开始生成' })).toBeInTheDocument();
    });

    it('should show form fields with default values', () => {
      render(<Generation />);

      const temperatureSlider = screen.getByText('创意程度');
      const maxTokensSlider = screen.getByText('最大长度');
      const categorySelect = screen.getByText('内容类型');

      expect(temperatureSlider).toBeInTheDocument();
      expect(maxTokensSlider).toBeInTheDocument();
      expect(categorySelect).toBeInTheDocument();
    });

    it('should submit generation request', async () => {
      render(<Generation />);

      const promptInput = screen.getByLabelText('输入您的需求');
      const submitButton = screen.getByRole('button', { name: '开始生成' });

      await mockUserInteraction.type(promptInput, 'Write a test article');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('生成中...')).toBeInTheDocument();
      });
    });

    it('should show validation error for empty prompt', async () => {
      render(<Generation />);

      const submitButton = screen.getByRole('button', { name: '开始生成' });
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('请输入生成需求')).toBeInTheDocument();
      });
    });

    it('should handle form options', async () => {
      render(<Generation />);

      const categorySelect = screen.getByRole('combobox');
      await mockUserInteraction.click(categorySelect);
      
      const articleOption = screen.getByText('文章');
      await mockUserInteraction.click(articleOption);

      expect(screen.getByDisplayValue('文章')).toBeInTheDocument();
    });
  });

  describe('查看现有生成', () => {
    beforeEach(() => {
      mockParams.id = 'test-generation-id';
    });

    it('should load existing generation', async () => {
      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByDisplayValue(mockGeneration.content)).toBeInTheDocument();
      });
    });

    it('should show loading state while fetching', () => {
      render(<Generation />);

      expect(screen.getByText('加载中...')).toBeInTheDocument();
    });

    it('should handle fetch error', async () => {
      server.use(
        rest.get('/api/generation/:id', (req, res, ctx) => {
          return res(ctx.status(404), ctx.json({ success: false }));
        })
      );

      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByText('生成记录不存在')).toBeInTheDocument();
      });
    });
  });

  describe('生成进度', () => {
    it('should show progress bar during generation', async () => {
      render(<Generation />);

      const promptInput = screen.getByLabelText('输入您的需求');
      const submitButton = screen.getByRole('button', { name: '开始生成' });

      await mockUserInteraction.type(promptInput, 'Test prompt');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByRole('progressbar')).toBeInTheDocument();
      });
    });

    it('should update progress via socket events', async () => {
      render(<Generation />);

      // 模拟socket事件监听
      expect(mockSocketEvents.on).toHaveBeenCalledWith('generation:progress', expect.any(Function));
      expect(mockSocketEvents.on).toHaveBeenCalledWith('generation:completed', expect.any(Function));
      expect(mockSocketEvents.on).toHaveBeenCalledWith('generation:failed', expect.any(Function));
    });

    it('should handle generation completion', async () => {
      render(<Generation />);

      // 获取progress事件处理器
      const progressHandler = mockSocketEvents.on.mock.calls.find(
        call => call[0] === 'generation:completed'
      )?.[1];

      if (progressHandler) {
        progressHandler({
          id: 'test-id',
          content: 'Generated content',
          images: [],
          metadata: {},
        });
      }

      await waitFor(() => {
        expect(screen.getByText('Generated content')).toBeInTheDocument();
      });
    });

    it('should handle generation failure', async () => {
      render(<Generation />);

      const failureHandler = mockSocketEvents.on.mock.calls.find(
        call => call[0] === 'generation:failed'
      )?.[1];

      if (failureHandler) {
        failureHandler({
          id: 'test-id',
          error: 'Generation failed',
        });
      }

      await waitFor(() => {
        expect(screen.getByText('生成失败')).toBeInTheDocument();
        expect(screen.getByText('Generation failed')).toBeInTheDocument();
      });
    });
  });

  describe('生成结果', () => {
    it('should display generated content', async () => {
      mockParams.id = 'test-generation-id';
      
      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByText(mockGeneration.content)).toBeInTheDocument();
      });
    });

    it('should show copy button for completed generation', async () => {
      mockParams.id = 'test-generation-id';
      
      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: '复制' })).toBeInTheDocument();
      });
    });

    it('should copy content to clipboard', async () => {
      mockParams.id = 'test-generation-id';
      
      render(<Generation />);

      await waitFor(() => {
        const copyButton = screen.getByRole('button', { name: '复制' });
        mockUserInteraction.click(copyButton);
      });

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith(mockGeneration.content);
    });

    it('should show download button', async () => {
      mockParams.id = 'test-generation-id';
      
      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: '下载' })).toBeInTheDocument();
      });
    });

    it('should render markdown content', async () => {
      const markdownContent = '# Test Title\n\nTest content with **bold** text.';
      
      server.use(
        rest.get('/api/generation/:id', (req, res, ctx) => {
          return res(
            ctx.json({
              success: true,
              data: {
                ...mockGeneration,
                content: markdownContent,
              },
            })
          );
        })
      );

      mockParams.id = 'test-generation-id';
      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
        expect(screen.getByText('Test Title')).toBeInTheDocument();
      });
    });

    it('should display generation metadata', async () => {
      mockParams.id = 'test-generation-id';
      
      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByText('生成信息')).toBeInTheDocument();
        expect(screen.getByText(/生成时间:/)).toBeInTheDocument();
      });
    });
  });

  describe('重新生成', () => {
    beforeEach(() => {
      mockParams.id = 'test-generation-id';
    });

    it('should show regenerate button for existing generation', async () => {
      render(<Generation />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: '重新生成' })).toBeInTheDocument();
      });
    });

    it('should handle regenerate action', async () => {
      render(<Generation />);

      await waitFor(() => {
        const regenerateButton = screen.getByRole('button', { name: '重新生成' });
        mockUserInteraction.click(regenerateButton);
      });

      await waitFor(() => {
        expect(screen.getByText('生成中...')).toBeInTheDocument();
      });
    });

    it('should disable regenerate during generation', async () => {
      render(<Generation />);

      const promptInput = screen.getByLabelText('输入您的需求');
      const submitButton = screen.getByRole('button', { name: '开始生成' });

      await mockUserInteraction.type(promptInput, 'Test prompt');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        const regenerateButton = screen.getByRole('button', { name: '重新生成' });
        expect(regenerateButton).toBeDisabled();
      });
    });
  });

  describe('响应式设计', () => {
    it('should render properly on mobile', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<Generation />);

      expect(screen.getByText('内容生成')).toBeInTheDocument();
      expect(screen.getByText('生成结果')).toBeInTheDocument();
    });

    it('should stack columns on small screens', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });

      render(<Generation />);

      const containers = screen.getAllByRole('main');
      expect(containers.length).toBeGreaterThan(0);
    });
  });

  describe('错误处理', () => {
    it('should handle API errors gracefully', async () => {
      server.use(
        rest.post('/api/generation', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ success: false, message: 'Server error' }));
        })
      );

      render(<Generation />);

      const promptInput = screen.getByLabelText('输入您的需求');
      const submitButton = screen.getByRole('button', { name: '开始生成' });

      await mockUserInteraction.type(promptInput, 'Test prompt');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Server error')).toBeInTheDocument();
      });
    });

    it('should handle network errors', async () => {
      server.use(
        rest.post('/api/generation', (req, res, ctx) => {
          return res.networkError('Network error');
        })
      );

      render(<Generation />);

      const promptInput = screen.getByLabelText('输入您的需求');
      const submitButton = screen.getByRole('button', { name: '开始生成' });

      await mockUserInteraction.type(promptInput, 'Test prompt');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/网络错误/)).toBeInTheDocument();
      });
    });
  });

  describe('清理', () => {
    it('should cleanup socket listeners on unmount', () => {
      const { unmount } = render(<Generation />);

      unmount();

      expect(mockSocketEvents.off).toHaveBeenCalledWith('generation:status', expect.any(Function));
      expect(mockSocketEvents.off).toHaveBeenCalledWith('generation:completed', expect.any(Function));
      expect(mockSocketEvents.off).toHaveBeenCalledWith('generation:failed', expect.any(Function));
      expect(mockSocketEvents.off).toHaveBeenCalledWith('generation:progress', expect.any(Function));
    });
  });
});
