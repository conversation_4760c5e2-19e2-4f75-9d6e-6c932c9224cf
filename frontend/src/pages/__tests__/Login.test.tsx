import { render, screen, waitFor } from '@/test/utils';
import { mockUserInteraction, expectSuccessMessage, expectErrorMessage, expectNavigation, clearNavigateMock } from '@/test/utils';
import Login from '@/pages/Login';
import { server, errorHandlers } from '@/test/mocks/server';

// Mock useAuth hook
const mockLogin = jest.fn();
const mockAuthContext = {
  user: null,
  token: null,
  loading: false,
  login: mockLogin,
  register: jest.fn(),
  logout: jest.fn(),
  refreshToken: jest.fn(),
  updateUser: jest.fn(),
};

jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => mockAuthContext,
}));

describe('Login Page', () => {
  beforeEach(() => {
    clearNavigateMock();
    mockLogin.mockClear();
  });

  describe('渲染', () => {
    it('should render login form', () => {
      render(<Login />);

      expect(screen.getByText('Coze Clone Platform')).toBeInTheDocument();
      expect(screen.getByText('登录您的账户')).toBeInTheDocument();
      expect(screen.getByLabelText('邮箱')).toBeInTheDocument();
      expect(screen.getByLabelText('密码')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument();
    });

    it('should render navigation links', () => {
      render(<Login />);

      expect(screen.getByText('立即注册')).toBeInTheDocument();
      expect(screen.getByText('忘记密码？')).toBeInTheDocument();
    });

    it('should redirect if user is already logged in', () => {
      mockAuthContext.user = { id: '1', username: 'test', email: '<EMAIL>' };

      render(<Login />);

      expectNavigation('/', { replace: true });
    });
  });

  describe('表单验证', () => {
    it('should show validation errors for empty fields', async () => {
      render(<Login />);

      const submitButton = screen.getByRole('button', { name: '登录' });
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('请输入邮箱')).toBeInTheDocument();
        expect(screen.getByText('请输入密码')).toBeInTheDocument();
      });
    });

    it('should show validation error for invalid email', async () => {
      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const submitButton = screen.getByRole('button', { name: '登录' });

      await mockUserInteraction.type(emailInput, 'invalid-email');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument();
      });
    });

    it('should not submit with invalid data', async () => {
      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const submitButton = screen.getByRole('button', { name: '登录' });

      await mockUserInteraction.type(emailInput, 'invalid-email');
      await mockUserInteraction.click(submitButton);

      expect(mockLogin).not.toHaveBeenCalled();
    });
  });

  describe('登录流程', () => {
    it('should submit login form with valid data', async () => {
      mockLogin.mockResolvedValue(undefined);

      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const passwordInput = screen.getByLabelText('密码');
      const submitButton = screen.getByRole('button', { name: '登录' });

      await mockUserInteraction.type(emailInput, '<EMAIL>');
      await mockUserInteraction.type(passwordInput, 'password123');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });

      expectNavigation('/');
    });

    it('should show loading state during login', async () => {
      let resolveLogin: () => void;
      const loginPromise = new Promise<void>((resolve) => {
        resolveLogin = resolve;
      });
      mockLogin.mockReturnValue(loginPromise);

      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const passwordInput = screen.getByLabelText('密码');
      const submitButton = screen.getByRole('button', { name: '登录' });

      await mockUserInteraction.type(emailInput, '<EMAIL>');
      await mockUserInteraction.type(passwordInput, 'password123');
      await mockUserInteraction.click(submitButton);

      expect(screen.getByText('登录中...')).toBeInTheDocument();
      expect(submitButton).toBeDisabled();

      resolveLogin!();
      await waitFor(() => {
        expect(screen.getByText('登录')).toBeInTheDocument();
      });
    });

    it('should handle login error', async () => {
      const errorMessage = '登录失败，请检查邮箱和密码';
      mockLogin.mockRejectedValue(new Error(errorMessage));

      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const passwordInput = screen.getByLabelText('密码');
      const submitButton = screen.getByRole('button', { name: '登录' });

      await mockUserInteraction.type(emailInput, '<EMAIL>');
      await mockUserInteraction.type(passwordInput, 'wrongpassword');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      expect(mockLogin).toHaveBeenCalled();
    });

    it('should clear error when user dismisses it', async () => {
      const errorMessage = '登录失败';
      mockLogin.mockRejectedValue(new Error(errorMessage));

      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const passwordInput = screen.getByLabelText('密码');
      const submitButton = screen.getByRole('button', { name: '登录' });

      await mockUserInteraction.type(emailInput, '<EMAIL>');
      await mockUserInteraction.type(passwordInput, 'wrongpassword');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      const closeButton = screen.getByRole('button', { name: 'Close' });
      await mockUserInteraction.click(closeButton);

      expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
    });
  });

  describe('重定向处理', () => {
    it('should redirect to intended page after login', async () => {
      mockLogin.mockResolvedValue(undefined);

      const mockLocation = {
        pathname: '/login',
        search: '',
        hash: '',
        state: { from: { pathname: '/generation' } },
      };

      jest.mock('react-router-dom', () => ({
        ...jest.requireActual('react-router-dom'),
        useLocation: () => mockLocation,
      }));

      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const passwordInput = screen.getByLabelText('密码');
      const submitButton = screen.getByRole('button', { name: '登录' });

      await mockUserInteraction.type(emailInput, '<EMAIL>');
      await mockUserInteraction.type(passwordInput, 'password123');
      await mockUserInteraction.click(submitButton);

      await waitFor(() => {
        expectNavigation('/generation', { replace: true });
      });
    });
  });

  describe('键盘交互', () => {
    it('should submit form on Enter key', async () => {
      mockLogin.mockResolvedValue(undefined);

      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱');
      const passwordInput = screen.getByLabelText('密码');

      await mockUserInteraction.type(emailInput, '<EMAIL>');
      await mockUserInteraction.type(passwordInput, 'password123{enter}');

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalled();
      });
    });
  });

  describe('无障碍性', () => {
    it('should have proper form labels', () => {
      render(<Login />);

      expect(screen.getByLabelText('邮箱')).toBeInTheDocument();
      expect(screen.getByLabelText('密码')).toBeInTheDocument();
    });

    it('should have proper button roles', () => {
      render(<Login />);

      expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument();
    });

    it('should have proper link roles', () => {
      render(<Login />);

      expect(screen.getByRole('link', { name: '立即注册' })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: '忘记密码？' })).toBeInTheDocument();
    });
  });

  describe('响应式设计', () => {
    it('should render properly on mobile', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      render(<Login />);

      const form = screen.getByRole('form');
      expect(form).toBeInTheDocument();
    });
  });

  describe('安全性', () => {
    it('should not expose password in DOM', () => {
      render(<Login />);

      const passwordInput = screen.getByLabelText('密码') as HTMLInputElement;
      expect(passwordInput.type).toBe('password');
    });

    it('should have autocomplete attributes', () => {
      render(<Login />);

      const emailInput = screen.getByLabelText('邮箱') as HTMLInputElement;
      const passwordInput = screen.getByLabelText('密码') as HTMLInputElement;

      expect(emailInput.autocomplete).toBe('email');
      expect(passwordInput.autocomplete).toBe('current-password');
    });
  });
});
