import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { io, Socket } from 'socket.io-client';
import { message } from 'antd';
import { useAuth } from './useAuth';
import { SocketEvents } from '@/types';

interface SocketContextType {
  socket: Socket | null;
  connected: boolean;
  joinRoom: (room: string) => void;
  leaveRoom: (room: string) => void;
  emit: <K extends keyof SocketEvents>(event: K, data: SocketEvents[K]) => void;
  on: <K extends keyof SocketEvents>(event: K, callback: (data: SocketEvents[K]) => void) => void;
  off: <K extends keyof SocketEvents>(event: K, callback?: (data: SocketEvents[K]) => void) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const { user, token } = useAuth();

  useEffect(() => {
    if (!user || !token) {
      // 用户未登录，断开socket连接
      if (socket) {
        socket.disconnect();
        setSocket(null);
        setConnected(false);
      }
      return;
    }

    // 创建socket连接
    const newSocket = io(import.meta.env.VITE_API_URL || '', {
      auth: {
        token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
    });

    // 连接事件监听
    newSocket.on('connect', () => {
      console.log('Socket connected:', newSocket.id);
      setConnected(true);
      
      // 加入用户房间
      newSocket.emit('join', `user_${user.id}`);
    });

    newSocket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setConnected(false);
      
      if (reason === 'io server disconnect') {
        // 服务器主动断开连接，需要手动重连
        newSocket.connect();
      }
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setConnected(false);
      
      if (error.message.includes('Authentication')) {
        message.error('Socket认证失败，请重新登录');
      }
    });

    newSocket.on('reconnect', (attemptNumber) => {
      console.log('Socket reconnected after', attemptNumber, 'attempts');
      setConnected(true);
      message.success('连接已恢复');
    });

    newSocket.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error);
    });

    newSocket.on('reconnect_failed', () => {
      console.error('Socket reconnection failed');
      message.error('连接失败，请刷新页面重试');
    });

    // 业务事件监听
    newSocket.on('generation:status', (data) => {
      console.log('Generation status update:', data);
      // 这里可以触发全局状态更新或通知
    });

    newSocket.on('generation:completed', (data) => {
      console.log('Generation completed:', data);
      message.success('内容生成完成');
    });

    newSocket.on('generation:failed', (data) => {
      console.log('Generation failed:', data);
      message.error(`生成失败: ${data.error}`);
    });

    newSocket.on('generation:progress', (data) => {
      console.log('Generation progress:', data);
      // 可以在这里更新进度条
    });

    // 系统通知
    newSocket.on('notification', (data: {
      type: 'info' | 'success' | 'warning' | 'error';
      message: string;
      description?: string;
    }) => {
      message[data.type](data.message);
    });

    setSocket(newSocket);

    // 清理函数
    return () => {
      newSocket.disconnect();
      setSocket(null);
      setConnected(false);
    };
  }, [user, token]);

  // 加入房间
  const joinRoom = (room: string): void => {
    if (socket && connected) {
      socket.emit('join', room);
      console.log('Joined room:', room);
    }
  };

  // 离开房间
  const leaveRoom = (room: string): void => {
    if (socket && connected) {
      socket.emit('leave', room);
      console.log('Left room:', room);
    }
  };

  // 发送事件
  const emit = <K extends keyof SocketEvents>(event: K, data: SocketEvents[K]): void => {
    if (socket && connected) {
      socket.emit(event as string, data);
    } else {
      console.warn('Socket not connected, cannot emit event:', event);
    }
  };

  // 监听事件
  const on = <K extends keyof SocketEvents>(
    event: K,
    callback: (data: SocketEvents[K]) => void
  ): void => {
    if (socket) {
      socket.on(event as string, callback);
    }
  };

  // 取消监听事件
  const off = <K extends keyof SocketEvents>(
    event: K,
    callback?: (data: SocketEvents[K]) => void
  ): void => {
    if (socket) {
      if (callback) {
        socket.off(event as string, callback);
      } else {
        socket.off(event as string);
      }
    }
  };

  const value: SocketContextType = {
    socket,
    connected,
    joinRoom,
    leaveRoom,
    emit,
    on,
    off,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export default useSocket;
