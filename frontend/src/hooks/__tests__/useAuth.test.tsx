import { renderHook, waitFor } from '@testing-library/react';
import { useAuth } from '@/hooks/useAuth';
import { mockUser, mockAuthResponse, setLocalStorageItem, clearLocalStorageMock } from '@/test/utils';
import { server, errorHandlers } from '@/test/mocks/server';

describe('useAuth Hook', () => {
  beforeEach(() => {
    clearLocalStorageMock();
  });

  describe('初始化', () => {
    it('should initialize with no user when no stored data', async () => {
      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
    });

    it('should initialize with stored user data', async () => {
      setLocalStorageItem('token', 'stored-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe('stored-token');
    });

    it('should clear invalid stored data', async () => {
      setLocalStorageItem('token', 'invalid-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));

      // Mock API to return 401 for invalid token
      server.use(errorHandlers.unauthorized);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
    });
  });

  describe('登录', () => {
    it('should login successfully', async () => {
      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      await result.current.login(loginData);

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe(mockAuthResponse.token);
    });

    it('should handle login error', async () => {
      server.use(errorHandlers.unauthorized);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      await expect(result.current.login(loginData)).rejects.toThrow();
      expect(result.current.user).toBeNull();
    });

    it('should set loading state during login', async () => {
      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const loginPromise = result.current.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.current.loading).toBe(true);

      await loginPromise;

      expect(result.current.loading).toBe(false);
    });
  });

  describe('注册', () => {
    it('should register successfully', async () => {
      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
      };

      await result.current.register(registerData);

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe(mockAuthResponse.token);
    });

    it('should handle registration error', async () => {
      server.use(errorHandlers.serverError);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
      };

      await expect(result.current.register(registerData)).rejects.toThrow();
      expect(result.current.user).toBeNull();
    });
  });

  describe('登出', () => {
    it('should logout successfully', async () => {
      setLocalStorageItem('token', 'stored-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
      });

      result.current.logout();

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
    });

    it('should clear localStorage on logout', async () => {
      const { result } = renderHook(() => useAuth());

      // 先登录
      await result.current.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      // 然后登出
      result.current.logout();

      expect(localStorage.removeItem).toHaveBeenCalledWith('token');
      expect(localStorage.removeItem).toHaveBeenCalledWith('refreshToken');
      expect(localStorage.removeItem).toHaveBeenCalledWith('user');
    });
  });

  describe('刷新Token', () => {
    it('should refresh token successfully', async () => {
      setLocalStorageItem('refreshToken', 'refresh-token');

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await result.current.refreshToken();

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.token).toBe(mockAuthResponse.token);
    });

    it('should handle refresh token error', async () => {
      server.use(errorHandlers.unauthorized);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await expect(result.current.refreshToken()).rejects.toThrow();
      expect(result.current.user).toBeNull();
    });

    it('should logout on refresh token failure', async () => {
      setLocalStorageItem('token', 'stored-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));
      setLocalStorageItem('refreshToken', 'invalid-refresh-token');

      server.use(errorHandlers.unauthorized);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
      });

      await expect(result.current.refreshToken()).rejects.toThrow();

      expect(result.current.user).toBeNull();
      expect(result.current.token).toBeNull();
    });
  });

  describe('更新用户信息', () => {
    it('should update user successfully', async () => {
      setLocalStorageItem('token', 'stored-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
      });

      const updateData = {
        username: 'updateduser',
      };

      await result.current.updateUser(updateData);

      expect(result.current.user).toEqual(mockUser);
    });

    it('should handle update user error', async () => {
      setLocalStorageItem('token', 'stored-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));

      server.use(errorHandlers.serverError);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
      });

      const updateData = {
        username: 'updateduser',
      };

      await expect(result.current.updateUser(updateData)).rejects.toThrow();
    });
  });

  describe('自动刷新Token', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should automatically refresh token', async () => {
      setLocalStorageItem('token', 'stored-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
      });

      // 快进15分钟
      jest.advanceTimersByTime(15 * 60 * 1000);

      await waitFor(() => {
        expect(result.current.token).toBe(mockAuthResponse.token);
      });
    });

    it('should stop auto refresh on logout', async () => {
      setLocalStorageItem('token', 'stored-token');
      setLocalStorageItem('user', JSON.stringify(mockUser));

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.user).toEqual(mockUser);
      });

      // 登出
      result.current.logout();

      // 快进15分钟，不应该触发刷新
      jest.advanceTimersByTime(15 * 60 * 1000);

      expect(result.current.user).toBeNull();
    });
  });

  describe('错误处理', () => {
    it('should handle network errors', async () => {
      server.use(errorHandlers.networkError);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await expect(result.current.login({
        email: '<EMAIL>',
        password: 'password123',
      })).rejects.toThrow();
    });

    it('should handle server errors', async () => {
      server.use(errorHandlers.serverError);

      const { result } = renderHook(() => useAuth());

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await expect(result.current.login({
        email: '<EMAIL>',
        password: 'password123',
      })).rejects.toThrow();
    });
  });
});
