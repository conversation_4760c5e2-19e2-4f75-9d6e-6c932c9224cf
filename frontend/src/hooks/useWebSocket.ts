import { useEffect, useState, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { message } from 'antd';

interface WebSocketState {
  connected: boolean;
  socket: Socket | null;
  lastMessage: any;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
}

interface GenerationEvent {
  generationId: string;
  status?: string;
  content?: string;
  error?: string;
  prompt?: string;
}

export const useWebSocket = () => {
  const [state, setState] = useState<WebSocketState>({
    connected: false,
    socket: null,
    lastMessage: null,
    connectionStatus: 'disconnected'
  });

  const socketRef = useRef<Socket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = () => {
    if (socketRef.current?.connected) {
      return;
    }

    setState(prev => ({ ...prev, connectionStatus: 'connecting' }));

    const socket = io('http://localhost:8000', {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: maxReconnectAttempts,
      reconnectionDelay: 1000,
    });

    socket.on('connect', () => {
      console.log('✅ WebSocket connected');
      reconnectAttempts.current = 0;
      setState(prev => ({
        ...prev,
        connected: true,
        socket,
        connectionStatus: 'connected'
      }));
    });

    socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket disconnected:', reason);
      setState(prev => ({
        ...prev,
        connected: false,
        connectionStatus: 'disconnected'
      }));

      // 自动重连
      if (reason === 'io server disconnect') {
        // 服务器主动断开，不自动重连
        return;
      }

      if (reconnectAttempts.current < maxReconnectAttempts) {
        reconnectAttempts.current++;
        reconnectTimeoutRef.current = setTimeout(() => {
          console.log(`🔄 Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})`);
          connect();
        }, 2000 * reconnectAttempts.current);
      }
    });

    socket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection error:', error);
      setState(prev => ({
        ...prev,
        connected: false,
        connectionStatus: 'error'
      }));
    });

    socket.on('connected', (data) => {
      console.log('📡 WebSocket handshake completed:', data);
    });

    // 生成相关事件
    socket.on('generation_started', (data: GenerationEvent) => {
      console.log('🚀 Generation started:', data);
      message.info(`开始生成内容: ${data.prompt}`);
      setState(prev => ({ ...prev, lastMessage: { type: 'generation_started', data } }));
    });

    socket.on('generation_completed', (data: GenerationEvent) => {
      console.log('✅ Generation completed:', data);
      message.success('内容生成完成！');
      setState(prev => ({ ...prev, lastMessage: { type: 'generation_completed', data } }));
    });

    socket.on('generation_failed', (data: GenerationEvent) => {
      console.log('❌ Generation failed:', data);
      message.error(`生成失败: ${data.error}`);
      setState(prev => ({ ...prev, lastMessage: { type: 'generation_failed', data } }));
    });

    socket.on('generation_progress', (data: any) => {
      console.log('⏳ Generation progress:', data);
      setState(prev => ({ ...prev, lastMessage: { type: 'generation_progress', data } }));
    });

    socketRef.current = socket;
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }

    setState({
      connected: false,
      socket: null,
      lastMessage: null,
      connectionStatus: 'disconnected'
    });
  };

  const emit = (event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
    } else {
      console.warn('Cannot emit event: WebSocket not connected');
    }
  };

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, []);

  return {
    connected: state.connected,
    connectionStatus: state.connectionStatus,
    lastMessage: state.lastMessage,
    socket: state.socket,
    connect,
    disconnect,
    emit
  };
};

export default useWebSocket;
