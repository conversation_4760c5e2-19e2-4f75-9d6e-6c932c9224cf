import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { message } from 'antd';
import apiClient from '@/services/api';
import { User, LoginRequest, RegisterRequest, AuthResponse } from '@/types';

interface AuthContextType {
  user: User | null;
  token: string | null;
  loading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        const storedToken = localStorage.getItem('token');
        const storedUser = localStorage.getItem('user');

        if (storedToken && storedUser) {
          setToken(storedToken);
          setUser(JSON.parse(storedUser));
          
          // 验证token是否有效
          try {
            const userData = await apiClient.get('/auth/me');
            setUser(userData);
          } catch (error) {
            // Token无效，清除本地存储
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');
            setToken(null);
            setUser(null);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // 登录
  const login = async (credentials: LoginRequest): Promise<void> => {
    try {
      setLoading(true);
      const response: AuthResponse = await apiClient.post('/auth/login', credentials);
      
      const { user: userData, token: authToken, refreshToken: refreshTokenValue } = response;
      
      // 保存到本地存储
      localStorage.setItem('token', authToken);
      localStorage.setItem('refreshToken', refreshTokenValue);
      localStorage.setItem('user', JSON.stringify(userData));
      
      // 更新状态
      setToken(authToken);
      setUser(userData);
      
      message.success('登录成功');
    } catch (error: any) {
      message.error(error.message || '登录失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 注册
  const register = async (userData: RegisterRequest): Promise<void> => {
    try {
      setLoading(true);
      const response: AuthResponse = await apiClient.post('/auth/register', userData);
      
      const { user: newUser, token: authToken, refreshToken: refreshTokenValue } = response;
      
      // 保存到本地存储
      localStorage.setItem('token', authToken);
      localStorage.setItem('refreshToken', refreshTokenValue);
      localStorage.setItem('user', JSON.stringify(newUser));
      
      // 更新状态
      setToken(authToken);
      setUser(newUser);
      
      message.success('注册成功');
    } catch (error: any) {
      message.error(error.message || '注册失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 登出
  const logout = (): void => {
    try {
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      
      // 清除状态
      setToken(null);
      setUser(null);
      
      // 调用后端登出接口（可选）
      apiClient.post('/auth/logout').catch(() => {
        // 忽略错误，因为用户已经在前端登出
      });
      
      message.success('已退出登录');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // 刷新token
  const refreshToken = async (): Promise<void> => {
    try {
      const storedRefreshToken = localStorage.getItem('refreshToken');
      
      if (!storedRefreshToken) {
        throw new Error('No refresh token available');
      }
      
      const response: AuthResponse = await apiClient.post('/auth/refresh', {
        refreshToken: storedRefreshToken,
      });
      
      const { user: userData, token: newToken, refreshToken: newRefreshToken } = response;
      
      // 更新本地存储
      localStorage.setItem('token', newToken);
      localStorage.setItem('refreshToken', newRefreshToken);
      localStorage.setItem('user', JSON.stringify(userData));
      
      // 更新状态
      setToken(newToken);
      setUser(userData);
      
    } catch (error) {
      console.error('Token refresh error:', error);
      // 刷新失败，清除认证状态
      logout();
      throw error;
    }
  };

  // 更新用户信息
  const updateUser = async (userData: Partial<User>): Promise<void> => {
    try {
      const updatedUser: User = await apiClient.patch('/auth/profile', userData);
      
      // 更新本地存储
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      // 更新状态
      setUser(updatedUser);
      
      message.success('用户信息更新成功');
    } catch (error: any) {
      message.error(error.message || '更新用户信息失败');
      throw error;
    }
  };

  // 自动刷新token
  useEffect(() => {
    if (!token) return;

    const refreshInterval = setInterval(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error('Auto refresh token failed:', error);
        clearInterval(refreshInterval);
      }
    }, 15 * 60 * 1000); // 每15分钟刷新一次

    return () => clearInterval(refreshInterval);
  }, [token]);

  const value: AuthContextType = {
    user,
    token,
    loading,
    login,
    register,
    logout,
    refreshToken,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default useAuth;
