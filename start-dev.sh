#!/bin/bash

# Coze Clone Platform 开发环境启动脚本

set -e

echo "🚀 启动 Coze Clone Platform 开发环境..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)"
    exit 1
fi

echo "✅ 环境检查通过"

# 创建环境变量文件
echo "📝 创建环境变量文件..."

# 后端环境变量
if [ ! -f "backend/.env" ]; then
    cat > backend/.env << EOF
# 开发环境配置
NODE_ENV=development
PORT=8000

# 数据库配置
DATABASE_URL="postgresql://coze_user:coze_password@localhost:5432/coze_clone"
REDIS_URL="redis://localhost:6379"

# JWT 配置
JWT_SECRET="dev-super-secret-jwt-key-for-development-only-do-not-use-in-production"
JWT_EXPIRES_IN="24h"

# MCP Chrome 配置
MCP_CHROME_HOST="127.0.0.1"
MCP_CHROME_PORT="12306"
MCP_CHROME_TIMEOUT="30000"

# Coze 平台配置
COZE_BASE_URL="https://space.coze.cn"
COZE_API_TIMEOUT="30000"

# 前端配置
FRONTEND_URL="http://localhost:3000"
CORS_ORIGIN="http://localhost:3000"

# 日志配置
LOG_LEVEL="debug"
LOG_FILE_PATH="./logs"

# 安全配置
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX_REQUESTS="1000"

# 缓存配置
CACHE_TTL="3600"
CACHE_MAX_SIZE="1000"

# 文件上传配置
MAX_FILE_SIZE="10485760"
UPLOAD_PATH="./uploads"
EOF
    echo "✅ 创建后端环境变量文件"
fi

# 前端环境变量
if [ ! -f "frontend/.env" ]; then
    cat > frontend/.env << EOF
# API 配置
VITE_API_BASE_URL=http://localhost:8000/api
VITE_WS_URL=http://localhost:8000

# 应用配置
VITE_APP_NAME="Coze Clone Platform"
VITE_APP_VERSION="1.0.0"
VITE_APP_DESCRIPTION="AI Content Generation Platform"

# 功能开关
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false
EOF
    echo "✅ 创建前端环境变量文件"
fi

# 启动数据库服务
echo "🗄️ 启动数据库服务..."
docker-compose -f docker-compose.dev.yml up -d

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 检查数据库连接
echo "🔍 检查数据库连接..."
until docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U coze_user -d coze_clone; do
    echo "等待 PostgreSQL 启动..."
    sleep 2
done

echo "✅ 数据库已启动"

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    npm install
fi

# 生成 Prisma 客户端
echo "🔧 生成 Prisma 客户端..."
npx prisma generate

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
npx prisma migrate deploy

# 运行种子数据
echo "🌱 运行种子数据..."
npx prisma db seed

cd ..

# 安装前端依赖
echo "📦 安装前端依赖..."
cd frontend
if [ ! -d "node_modules" ]; then
    npm install
fi
cd ..

# 创建日志目录
mkdir -p backend/logs
mkdir -p backend/uploads

echo "🎉 环境准备完成！"
echo ""
echo "📋 启动说明："
echo "1. 启动后端服务: cd backend && npm run dev"
echo "2. 启动前端服务: cd frontend && npm run dev"
echo "3. 访问应用: http://localhost:3000"
echo ""
echo "🔧 开发工具："
echo "- 数据库管理: npx prisma studio (在 backend 目录下)"
echo "- API 文档: http://localhost:8000/api/health"
echo "- Redis 管理: redis-cli (连接 localhost:6379)"
echo ""
echo "🛑 停止服务："
echo "- 停止数据库: docker-compose -f docker-compose.dev.yml down"
echo ""

# 询问是否自动启动服务
read -p "是否自动启动后端和前端服务？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动后端服务..."
    cd backend
    npm run dev &
    BACKEND_PID=$!
    
    echo "⏳ 等待后端服务启动..."
    sleep 5
    
    echo "🚀 启动前端服务..."
    cd ../frontend
    npm run dev &
    FRONTEND_PID=$!
    
    echo ""
    echo "🎉 服务启动完成！"
    echo "📱 前端地址: http://localhost:3000"
    echo "🔧 后端地址: http://localhost:8000"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    # 等待用户中断
    trap "echo ''; echo '🛑 停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose -f docker-compose.dev.yml down; echo '✅ 服务已停止'; exit 0" INT
    
    wait
else
    echo "✅ 环境已准备完成，请手动启动服务"
fi
